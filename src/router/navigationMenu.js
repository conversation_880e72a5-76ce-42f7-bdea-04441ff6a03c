import { PRIV_CODES } from "@/constants/privilegeCodes";
import { checkUserPrivilege } from "./middleware";

const navigationMenuItems = [
    {
        id: "Action_Center",
        prependIcon: "mdi-bell-alert",
        title: "Action Center",
        to: {
            name: "action-center",
        },
        privilege_code: PRIV_CODES.DASH_PUR,
    },
    {
        id: "Dashboards",
        prependIcon: "mdi-view-dashboard",
        title: "Dashboards",
        subItems: [
            {
                id: "Inventory_Dashboard",
                prependIcon: "mdi-warehouse",
                title: "Inventory Dashboard",
                to: {
                    name: "inventory-dashboard",
                },
                privilege_code: PRIV_CODES.DASH_INV,
                disabled: true
            },
            {
                id: "Purchase_Dashboard",
                prependIcon: "mdi-cart-arrow-down",
                title: "Purchase Dashboard",
                to: {
                    name: "purchase-dashboard",
                },
                privilege_code: PRIV_CODES.DASH_PUR,
            },
            {
                id: "COGS_Dashboard",
                prependIcon: "mdi-cash-multiple",
                title: "COGS Dashboard",
                to: {
                    name: "cogs-dashboard",
                },
                privilege_code: PRIV_CODES.DASH_COGS,
                disabled: true
            },
        ],
    },
    {
        id: "Product",
        prependIcon: "mdi-wrench-cog",
        title: "Product",
        privilege_code: PRIV_CODES.PC_VIEW,
        subItems: [
            {
                id: "Import_Export",
                prependIcon: "mdi-database-export",
                title: "Import & Export",
                to: {
                    name: "import-export",
                },
                privilege_code: PRIV_CODES.PC_IMPORT,
            },
            {
                id: "Tags",
                prependIcon: "mdi-tag",
                title: "Tags",
                to: {
                    name: "tags",
                },
            },
            {
                id: "Vendors",
                prependIcon: "mdi-store",
                title: "Vendors",
                to: {
                    name: "vendors",
                },
                actionRoute: "create-vendor",
                appendIcon: "mdi-plus-circle-outline",
            },
            {
                id: "Categories",
                prependIcon: "mdi-shape",
                title: "Categories",
                to: {
                    name: "categories",
                },
                actionRoute: "create-category",
                appendIcon: "mdi-plus-circle-outline",
            },
            {
                id: "House_Units",
                prependIcon: "mdi-scale",
                title: "House Units",
                to: {
                    name: "house-units",
                },
            },
            {
                id: "Taxes",
                prependIcon: "mdi-calculator",
                title: "Taxes",
                to: {
                    name: "taxes",
                },
            },
            {
                id: "Inventory_Items",
                prependIcon: "mdi-package-variant",
                title: "Inventory Items",
                to: {
                    name: "inventory-items",
                },
                actionRoute: "create-inventory-item",
                appendIcon: "mdi-plus-circle-outline",
            },
            {
                id: "Recipes",
                prependIcon: "mdi-food-variant",
                title: "Recipes",
                to: {
                    name: "recipes",
                },
                actionRoute: "create-recipe",
                appendIcon: "mdi-plus-circle-outline",
            },
        ],
    },
    {
        id: "POS",
        prependIcon: "mdi-network-pos",
        title: "POS",
        subItems: [
            {
                id: "Menu_Items",
                prependIcon: "mdi-food",
                title: "Menu Items",
                to: {
                    name: "menu-items",
                },
                disabled: true
            },
            {
                id: "Modifiers",
                prependIcon: "mdi-food-fork-drink",
                title: "Modifiers",
                to: {
                    name: "modifiers",
                },
                disabled: true
            },
        ],
    },
    {
        id: "Procurement",
        prependIcon: "mdi-package-variant-closed",
        title: "Procurement",
        subItems: [
            {
                id: "Purchase_Requests_PR",
                prependIcon: "mdi-cart-arrow-up",
                title: "Purchase Requests",
                to: {
                    name: "purchase-requests",
                },
                privilege_code: PRIV_CODES.PUR_PR,
                actionRoute: "create-purchase-request",
                appendIcon: "mdi-plus-circle-outline",

            },
            {
                id: "Purchase_Order_PO",
                prependIcon: "mdi-package",
                title: "Purchase Order",
                to: {
                    name: "purchase-orders",
                },
                privilege_code: PRIV_CODES.PUR_PO,
                actionRoute: "create-purchase-order",
                appendIcon: "mdi-plus-circle-outline",
            },
            {
                id: "Goods_Received_Note_GRN",
                prependIcon: "mdi-invoice-text-multiple-outline",
                title: "Goods Received Note",
                to: {
                    name: "goods-received-notes",
                },
                privilege_code: PRIV_CODES.PUR_GRN,
            },

        ],
    },
    {
        id: "Stock Movement",
        prependIcon: "mdi-clipboard-list",
        title: "Stock Movement",
        subItems: [
            {
                id: "Transfers",
                prependIcon: "mdi-transfer",
                title: "Transfers",
                to: {
                    name: "transfers",
                },
                privilege_code: PRIV_CODES.PUR_INDENT,
                actionRoute: "create-transfer",
                appendIcon: "mdi-plus-circle-outline",
            },
            {
                id: "Spoilage",
                prependIcon: "mdi-delete-alert",
                title: "Spoilage",
                to: {
                    name: "spoilage",
                },
                privilege_code: PRIV_CODES.PC_EDIT,
                actionRoute: "create-spoilage",
                appendIcon: "mdi-plus-circle-outline",
                disabled: true
            },
            {
                id: "Adjustment",
                prependIcon: "mdi-scale-balance",
                title: "Adjustment",
                to: {
                    name: "adjustment",
                },
                privilege_code: PRIV_CODES.PC_EDIT,
                actionRoute: "create-adjustment",
                appendIcon: "mdi-plus-circle-outline",
                disabled: true
            },
            {
                id: "Closing",
                prependIcon: "mdi-folder-lock",
                title: "Closing",
                to: {
                    name: "closing",
                },
                privilege_code: PRIV_CODES.PC_EDIT,
                actionRoute: "create-closing",
                appendIcon: "mdi-plus-circle-outline",
                disabled: false
            },
        ],
    },
    {
        id: "Stocks",
        prependIcon: "mdi-chart-box-outline",
        title: "Stocks",
        privilege_code: PRIV_CODES.STK_VIEW,
        subItems: [
            {
                id: "Stocks_List",
                prependIcon: "mdi-archive-outline",
                title: "Stocks",
                to: {
                    name: "stocks",
                },
            },
            {
                id: "Stock_Ledgers",
                prependIcon: "mdi-list-box-outline",
                title: "Stock ledgers",
                to: {
                    name: "stock-ledgers",
                },
            },
        ],
    },
    {
        id: "Reports",
        prependIcon: "mdi-chart-box-outline",
        title: "Reports",
        subItems: [
            {
                id: "grn-report",
                prependIcon: "mdi-invoice-text-multiple-outline",
                title: "GRN Report",
                to: {
                    name: "grn-report"
                },
                privilege_code: PRIV_CODES.REP_GRN,
            },
            {
                id: "grn-detailed-report",
                prependIcon: "mdi-file-document-outline",
                title: "Detailed GRN Report",
                to: {
                    name: "grn-detailed-report"
                },
                privilege_code: PRIV_CODES.REP_GRN,
            },
            {
                id: "grn-location-wise-report",
                prependIcon: "mdi-map-marker-outline",
                title: "Location-wise GRN Report",
                to: {
                    name: "grn-location-wise-report"
                },
                privilege_code: PRIV_CODES.REP_GRN,
            },
            {
                id: "grn-vendor-wise-report",
                prependIcon: "mdi-account-group-outline",
                title: "Vendor-wise GRN Report",
                to: {
                    name: "grn-vendor-wise-report"
                },
                privilege_code: PRIV_CODES.REP_GRN,
            },
            {
                id: "grn-category-wise-report",
                prependIcon: "mdi-shape-outline",
                title: "Category-wise GRN Report",
                to: {
                    name: "grn-category-wise-report"
                },
                privilege_code: PRIV_CODES.REP_GRN,
            },
            {
                id: "grn-item-wise-report",
                prependIcon: "mdi-cube-outline",
                title: "Item-wise GRN Report",
                to: {
                    name: "grn-item-wise-report"
                },
                privilege_code: PRIV_CODES.REP_GRN,
            },
            {
                id: "grn-daily-report",
                prependIcon: "mdi-calendar-outline",
                title: "Daily GRN Report",
                to: {
                    name: "grn-daily-report"
                },
                privilege_code: PRIV_CODES.REP_GRN,
            },
            {
                id: "Transfer_List_Report",
                prependIcon: "mdi-transfer",
                title: "Transfer Report",
                to: {
                    name: "transfer-list-report"
                },
                // privilege_code: PRIV_CODES.REP_GRN,
            },
            {
                id: "Dispatch_Transfer_Report",
                prependIcon: "mdi-truck",
                title: "Dispatch Transfer Report",
                to: {
                    name: "dispatch-transfer-report"
                },
                // privilege_code: PRIV_CODES.REP_GRN,
            },
            {
                id: "Detailed_Transfer_Report",
                prependIcon: "mdi-repeat",
                title: "Detailed Transfer Report",
                to: {
                    name: "detailed-transfer-report"
                },
                // privilege_code: PRIV_CODES.REP_GRN,
            },
        ],
    },
    {
        id: "Templates & Contracts",
        prependIcon: "mdi-clipboard-text-outline",
        title: "Templates & Contracts",
        subItems: [
            {
                id: "Contract",
                prependIcon: "mdi-file-sign",
                title: "Contract",
                to: {
                    name: "contract",
                },
                privilege_code: PRIV_CODES.SET_CT,
                actionRoute: "create-contract",
                appendIcon: "mdi-plus-circle-outline",
            },
            {
                id: "Purchase Template",
                prependIcon: "mdi-package",
                title: "PR Template",
                // to: {
                //     name: "pr-template",
                // },
                privilege_code: PRIV_CODES.PUR_PO,
                // actionRoute: "create-pr-template",
                appendIcon: "mdi-plus-circle-outline",
                disabled: true
            },

        ],
    },
    {
        id: "Setup",
        prependIcon: "mdi-cog-outline",
        title: "Setup",
        subItems: [
            {
                id: "Locations",
                prependIcon: "mdi-store-marker-outline",
                title: "Locations",
                to: {
                    name: "locations",
                },
                privilege_code: PRIV_CODES.SET_LOC,
            },
            {
                id: "Work_Storage_Area",
                prependIcon: "mdi-domain",
                title: "Work/Storage Area",
                to: {
                    name: "work-areas",
                },
                privilege_code: PRIV_CODES.SET_LOC,
            },
            {
                id: "Roles",
                prependIcon: "mdi-account-key",
                title: "Roles",
                to: {
                    name: "roles",
                },
                actionRoute: "create-role",
                appendIcon: "mdi-plus-circle-outline",
                privilege_code: PRIV_CODES.SET_USER,
            },
            {
                id: "Users",
                prependIcon: "mdi-account",
                title: "Users",
                to: {
                    name: "users",
                },
                privilege_code: PRIV_CODES.SET_USER,
            },
        ],
    },
];

/**
 * Get the navigation menu for a user.
 *
 * @param {Object} options
 * @param {boolean} options.isAdmin - If true, returns full menu without filtering.
 * @param {Array<string>} options.privileges - User's privilege codes.
 * @returns {Array<Object>} Filtered navigation menu (or full menu if admin).
 */
export function getNavigationMenus(isAdmin = false, privileges = []) {
    // Admins get full access to all menus
    if (isAdmin) {
        return navigationMenuItems;
    }

    /**
     * Recursively filters menu items based on user privileges.
     * - If the item has a privilege_code, it must match.
     * - If the item has subItems, keep only allowed ones.
     * - If a parent has no remaining subItems, it’s removed.
     */
    const filterByPrivileges = (items) =>
        items
            .map((item) => {
                // Skip if user lacks privilege
                if (!checkUserPrivilege(item.privilege_code)) {
                    return null;
                }

                // Filter subItems recursively
                if (item.subItems) {
                    const allowedSubItems = filterByPrivileges(item.subItems);
                    if (allowedSubItems.length > 0) {
                        return { ...item, subItems: allowedSubItems };
                    }
                    return null;
                }

                return item;
            })
            .filter(Boolean);

    return filterByPrivileges(navigationMenuItems);
}
