import { defineStore } from "pinia";
import { ref, computed } from "vue";
import httpClient from "@/plugin/Axios";
import { useSnackbarStore } from "./snackBar";
import { getTenantId } from "@/helpers/auth";

export const useSpoilageStore = defineStore("spoilage", () => {
  const tenantId = ref(getTenantId);

  const { showSnackbar } = useSnackbarStore();
  const spoilages = ref([]);

  const getSpoilages = computed(() => spoilages.value || []);
  const setSpoilages  = (data) => {
    spoilages.value = data;
  };

  const fetchSpoilages = async () => {
    try {
      const response = await httpClient.get(`spoilages`);
      setSpoilages(response.data);
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const fetchSpoilageById = async (id) => {
    try {
      const response = await httpClient.get(`spoilages/${id}`);
      return response.data;
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const createSpoilage = async (data) => {
    try {
      await httpClient.post("spoilages", {
        ...data,
        tenantId: tenantId.value,
      });
      showSnackbar("green", "Spoilage created successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  return {
    fetchSpoilages,
    fetchSpoilageById,
    createSpoilage,
    getSpoilages,
    setSpoilages,
  };
});
