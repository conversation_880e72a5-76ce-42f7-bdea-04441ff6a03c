<template>
  <div style="height: 100%">
    <page-loader v-if="loading" />

    <!-- Form Actions Bar -->
    <form-actions-bar
      v-if="!loading"
      @close="navigatePrevious"
      :hide-submit="true"
      :loading="loading"
    >
      <template #custom>
        <span v-if="cart.prNumber">#{{ cart.prNumber }}</span>
      </template>
      <template #actions>
        <v-btn
          text="Save draft"
          variant="outlined"
          color="primary"
          class="ml-2"
          @click="saveDraft"
          :loading="loading"
          :disabled="loading"
        />
        <v-btn
          text="Submit"
          @click="submitPR"
          variant="flat"
          color="primary"
          class="ml-2"
          :loading="loading"
          :disabled="loading"
        />
      </template>
    </form-actions-bar>

    <!-- Form -->
    <v-container v-if="!loading" fluid class="mt-2">
      <v-form ref="form" v-model="formValid">
        <v-row>
          <v-col cols="12" sm="6" md="3">
            <location-field
              ref="locationField"
              v-model="cart.location"
              hint="Location creating purchase"
              persistent-hint
              return-object
              mandatory
              hide-details="auto"
            />
          </v-col>
          <v-col cols="12" sm="6" md="3">
            <v-autocomplete
              v-model="cart.vendorType"
              :items="[
                { id: 1, name: 'All vendors' },
                { id: 2, name: 'Specific vendor' },
              ]"
              item-value="id"
              item-title="name"
              label="Vendor Type*"
              hide-details="auto"
              variant="outlined"
              density="compact"
              color="primary"
              :rules="[rules.require]"
              @update:model-value="onVendorTypeChange"
              clearable
            ></v-autocomplete>
          </v-col>
          <v-col v-if="cart.vendorType == 2" cols="12" sm="6" md="3">
            <vendor-field
              v-model="cart.vendorId"
              :mandatory="cart.vendorType == 2"
              @update:model-value="onChangeVendor"
            />
          </v-col>
          <v-col cols="12" sm="6" md="3">
            <v-date-input
              v-model="cart.deliveryDate"
              label="Delivery Date*"
              color="primary"
              variant="outlined"
              density="compact"
              hide-details="auto"
              prepend-icon
              prepend-inner-icon="$calendar"
              :rules="[rules.require]"
              hide-actions
            ></v-date-input>
          </v-col>
        </v-row>
      </v-form>

      <!-- Table -->
      <PRTable
        :pr-list="tableItems"
        :headers="prItemHeaders"
        :formValid="formValid"
        :showVendor="cart.vendorType !== 2"
        @removeItem="removeItem"
        @edit="modifyItem"
        @addItem="openForm = true"
      />

      <!-- Drawer Form -->
      <PRItemForm
        v-model="openForm"
        :form-valid="formValid"
        @add="addItem"
        :locationId="cart.location?.id"
        :selectedVendorId="cart.vendorId"
      />
    </v-container>
  </div>
</template>

<script setup>
import { ref, computed, onBeforeMount, inject, onMounted, nextTick } from "vue";

import FormActionsBar from "@/components/utils/FormActionsBar.vue";
import PageLoader from "@/components/utils/PageLoader.vue";
import LocationField from "@/components/fields/LocationField.vue";
import VendorField from "@/components/fields/VendorField.vue";
import PRItemForm from "@/components/purchase/PRItemForm.vue";
import PRTable from "@/components/purchase/PRTable.vue";

import { usePurchaseRequestStore } from "@/stores/purchaseRequest";
import { useMasterStore } from "@/stores/masterStore";

import { purchaseRequestItemHeaders } from "@/helpers/tableHeaders";
import cartHelper from "@/helpers/cartHelper";

import { useRoute, useRouter } from "vue-router";
import rules from "@/helpers/rules";
import { purchaseStatus } from "@/constants/status";

const purchaseRequestStore = usePurchaseRequestStore();
const masterStore = useMasterStore();

const vendors = computed(() => {
  return masterStore.getVendors();
});

const tableItems = ref([]);
const openForm = ref(false);
const form = ref(null);
const formValid = ref(false);

const $confirm = inject("confirm");
const locationField = ref(null);

const loading = ref(false);
const cart = ref({ ...cartHelper.NewCart("pr"), vendorType: 2 });

const route = useRoute();
const purchaseRequestId = route.params.id;
const isEdit = purchaseRequestId !== undefined;

const isClosed = computed(
  () =>
    cart.value.status === purchaseStatus.COMPLETED ||
    cart.value.status === purchaseStatus.REJECTED
);

const prItemHeaders = computed(() => {
  let headers = purchaseRequestItemHeaders;
  if (isClosed.value) {
    headers = purchaseRequestItemHeaders.filter((h) => h.key !== "actions");
  }
  if (cart.value.vendorType == 2)
    headers = headers.filter((h) => h.key !== "vendor.name");
  return headers;
});

const modifyItem = (item, index) => {
  cart.value = cartHelper.ModifyItem(cart.value, item, index);
  console.log(cart.value, "cat");
  tableItems.value = cart.value.items;
};

const addItem = (item) => {
  cart.value = cartHelper.AddItem(cart.value, item);
  tableItems.value = cart.value.items;
};

const removeItem = (index) => {
  cart.value = cartHelper.RemoveItem(cart.value, index);
  tableItems.value = cart.value.items;
};

const onVendorTypeChange = (value) => {
  cart.value.vendorType = value;
  if (value === 1) {
    cart.value.vendorId = null;
    cart.value.vendor = null;
  } else openForm.value = false;
};

const router = useRouter();

const navigatePrevious = () => {
  router.push({ name: "purchase-requests" });
};

const save = async (status) => {
  try {
    if (loading.value) return;
    loading.value = true;

    const { valid } = await form.value.validate();
    if (!valid) {
      loading.value = false;
      return;
    }

    const payload = {
      location: {
        id: cart.value.location?.id,
        name: cart.value.location?.name,
      },
      deliveryDate: cart.value.deliveryDate,
      vendorType: cart.value.vendorType,
      items: tableItems.value.map((item) => {
        const vendor =
          cart.value.vendorType === 2 ? cart.value.vendor : item.vendor;
        return { ...item, vendor: { id: vendor.id, name: vendor.name } };
      }),
      status,
      grossAmount: cart.value.grossAmount,
      totalDiscount: cart.value.totalDiscount,
      netAmount: cart.value.netAmount,
      totalChargeAmount: cart.value.totalChargeAmount,
      totalTaxAmount: cart.value.totalTaxAmount,
      totalAmount: cart.value.totalAmount,
      totalCess: cart.value.totalCess,
      totalFocAmount: cart.value.totalFocAmount,
      taxes: cart.value.taxes,
      charges: cart.value.charges,
    };

    if (isEdit) {
      payload.id = cart.value.id;
      payload.prNumber = cart.value.prNumber;
      payload.inventoryLocation = cart.value.inventoryLocation;
      payload.deliveryDate = new Date(cart.value.deliveryDate);
      payload.statusTimeline = cart.value.statusTimeline.map((timeline) => ({
        ...timeline,
        time: new Date(timeline.time),
      }));
      await purchaseRequestStore.updatePurchaseRequest(payload);
    } else {
      await purchaseRequestStore.createPurchaseRequest(payload);
    }
    navigatePrevious();
  } catch (err) {
    console.log(err);
  } finally {
    loading.value = false;
  }
};

const submitPR = async () => {
  if (!$confirm) {
    console.error("Global confirm dialog not available");
    return;
  }

  const confirmed = await $confirm(
    "Once you submit, you won’t be able to edit. Are you sure you want to continue?"
  );
  if (!confirmed) return;

  save(purchaseStatus.SUBMITTED);
};

const saveDraft = async () => {
  save(purchaseStatus.DRAFT);
};

const onChangeVendor = (vendorId) => {
  // @todo fetch vendor details
  cart.value.vendor = vendors.value.find((vendor) => vendor.id === vendorId);
};

onBeforeMount(async () => {
  loading.value = true;

  try {
    if (isEdit) {
      const result = await purchaseRequestStore.fetchPurchaseRequestById(
        purchaseRequestId
      );
      cart.value = { ...cartHelper.NewCart("pr"), ...result };
      tableItems.value = result.items;
      onVendorTypeChange(cart.value.vendorType);
      const vendor = result.items[0].vendor;
      cart.value.vendor = vendor;
      cart.value.vendorId = vendor.id;
    }

    loading.value = false;
  } catch (err) {
    console.error(err);
  }
});

let hasFocusedInitially = false;

const focusFirstField = () => {
  if (locationField.value?.$el) {
    const input = locationField.value.$el.querySelector(
      "input, .v-input input"
    );
    if (input) input.focus();
  }
};

onMounted(async () => {
  await nextTick();
  focusFirstField();
  hasFocusedInitially = true;

  window.addEventListener("keydown", (e) => {
    if (e.key === "Tab" && document.activeElement === document.body) {
      e.preventDefault();
      focusFirstField();
    }
  });
});
</script>
