<template>
  <v-card border rounded="lg" width="100%">
    <v-data-table
      class="table-bordered"
      :headers="headers"
      :items="items"
      items-per-page="-1"
      hide-default-footer
      hide-no-data
    >
      <template v-slot:item="{ item, index }">
        <tr>
          <td class="py-2 text-center">{{ index + 1 }}</td>
          <td class="py-2">
            <span>{{ item.name }}</span>
          </td>
          <td class="py-2 text-center">
            <span>{{ item.packageCode }}</span>
          </td>
          <td class="py-2">
            <v-text-field
              v-model="item.quantity"
              density="compact"
              type="number"
              variant="outlined"
              hide-details
              color="primary"
              @keydown.up.prevent
              @keydown.down.prevent
              :rules="[rules.require, rules.positive]"
              ><template v-slot:append-inner>
                {{ unit?.symbol || "" }}
              </template></v-text-field
            >
          </td>
          <td class="py-2 text-center">
            <span>{{ item.unitCost }}</span>
          </td>
          <td>
            <div class="d-flex justify-center align-center">
              <v-icon color="error" @click="removeRow(index)">
                mdi-close
              </v-icon>
            </div>
          </td>
        </tr>
      </template>
      <template v-slot:body.append>
        <tr>
          <td class="py-2"></td>
          <td class="py-2">
            <v-text-field
              v-model="newRow.name"
              density="compact"
              type="text"
              variant="outlined"
              hide-details
              color="primary"
            ></v-text-field>
          </td>
          <td class="py-2">
            <v-text-field
              v-model="newRow.packageCode"
              density="compact"
              type="text"
              variant="outlined"
              hide-details
              color="primary"
            ></v-text-field>
          </td>
          <td class="py-2">
            <v-text-field
              v-model="newRow.quantity"
              density="compact"
              type="number"
              variant="outlined"
              hide-details
              color="primary"
              :rules="[rules.positive]"
            >
              <template v-slot:append-inner>
                {{ unit?.symbol || "" }}
              </template></v-text-field
            >
          </td>
          <td class="py-2">
            <v-text-field
              v-model.number="newRow.unitCost"
              type="number"
              :rules="[rules.positive]"
              variant="outlined"
              density="compact"
              color="primary"
              hide-details="auto"
              @keydown.up.prevent
              @keydown.down.prevent
              @blur="newRow.unitCost = newRow.unitCost || 0"
              @keypress.enter="addRow(newRow)"
            ></v-text-field>
          </td>
          <td class="py-2">
            <div class="d-flex justify-center align-center">
              <v-icon
                color="green"
                :disabled="!isRowValid(newRow)"
                @click="commitNewRow"
              >
                mdi-plus
              </v-icon>
            </div>
          </td>
        </tr>
      </template>
    </v-data-table></v-card
  >
</template>
<script setup>
import { ref, nextTick } from "vue";
import { packageHeaders } from "@/helpers/tableHeaders";
import { packageRecord as DEFAULT_PACKAGE } from "@/helpers/defaultRecords";
import rules from "@/helpers/rules";

defineProps({
  unit: {
    type: Object,
    default: () => {},
  },
});

const items = defineModel();
// const inputRef = ref(null);
const headers = packageHeaders;

const newRow = ref({ ...DEFAULT_PACKAGE });

const isRowValid = (row) =>
  row.quantity > 0 &&
  row.name &&
  row.unitCost >= 0 &&
  row.packageCode !== undefined;

const commitNewRow = () => {
  if (!isRowValid(newRow.value)) return;
  items.value.push({ ...newRow.value });

  newRow.value = { ...DEFAULT_PACKAGE };
  // nextTick(() => {
  //   inputRef.value[0].focus();
  // });
};

const addRow = (row) => {
  if (!isRowValid(row)) return;
  commitNewRow();
};
const removeRow = (index) => {
  items.value.splice(index, 1);
};
</script>

<style scoped></style>
