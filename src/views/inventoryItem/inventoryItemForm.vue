<template>
  <div style="height: 100%">
    <!-- Form Actions Bar -->
    <form-actions-bar
      @close="navigatePrevious"
      @submit="save"
      :loading="isLoading"
    />

    <v-container v-if="!loader" fluid>
      <v-card-text class="pt-4">
        <v-form ref="form">
          <v-row>
            <v-col cols="4">
              <v-text-field
                v-model="record.itemName"
                label="Item Name*"
                color="primary"
                hide-details="auto"
                variant="outlined"
                density="compact"
                @blur="cleanName"
                :rules="[rules.require, rules.maxLength(100)]"
              ></v-text-field>
            </v-col>
            <v-col cols="4">
              <v-text-field
                v-model="record.itemCode"
                label="Item Code"
                color="primary"
                hide-details="auto"
                variant="outlined"
                density="compact"
                :rules="[rules.minLength(4), rules.maxLength(20)]"
              ></v-text-field>
            </v-col>
            <v-col cols="4">
              <v-autocomplete
                v-model="record.itemType"
                label="Item Type*"
                color="primary"
                hide-details="auto"
                variant="outlined"
                density="compact"
                :items="['bought', 'made']"
                :readonly="isEdit"
                @update:model-value="handleItemTypeChange"
                :rules="[rules.require]"
                clearable
              ></v-autocomplete>
            </v-col>
            <v-col cols="4">
              <CategoryField v-model="record.category" return-object/>
            </v-col>
            <v-col cols="4">
              <SubCategoryField v-model="record.subCategory" :categoryIds="[record.category?.id]" return-object />
            </v-col>
            <v-col cols="4">
              <TagField v-model="record.tags" return-object multiple />
            </v-col>
            <v-col cols="4">
              <v-autocomplete
                v-model="record.purchaseUnit"
                label="Purchase Unit*"
                color="primary"
                hide-details="auto"
                variant="outlined"
                density="compact"
                :items="purchaseUnits"
                item-title="name"
                item-value="id"
                return-object
                @update:model-value="handlePurchaseUnitChange"
                :rules="[rules.require]"
                clearable
              >
                <template v-slot:item="{ props, item }">
                  <v-list-item
                    v-bind="props"
                    :title="`${item.raw.name} (${item.raw.symbol})`"
                  >
                  </v-list-item>
                </template>
                <template #selection="{ item }">
                  {{ item.raw.name }} ({{ item.raw.symbol }})
                </template>
              </v-autocomplete>
            </v-col>
            <v-col cols="4">
              <v-autocomplete
                v-model="countingUnit"
                :items="countingUnitOptions"
                hide-details="auto"
                variant="outlined"
                density="compact"
                label="Counting Unit*"
                item-title="name"
                item-value="symbol"
                return-object
                color="primary"
                :rules="[
                  rules.require,
                  (v) =>
                    countingUnit?.conversion
                      ? true
                      : 'No conversion found for selected unit',
                ]"
                ref="countingUnitRef"
                clearable
              >
                <template #prepend-inner>
                  <span v-if="countingUnit?.conversion" class="d-flex"
                    >{{ countingUnit?.conversion }}&nbsp; <span>x</span>
                  </span>
                </template>
              </v-autocomplete>
            </v-col>
            <v-col cols="4">
              <v-autocomplete
                v-model="recipeUnit"
                :items="countingUnitOptions"
                hide-details="auto"
                variant="outlined"
                density="compact"
                label="Recipe Unit*"
                item-title="name"
                item-value="symbol"
                return-object
                color="primary"
                :rules="[
                  rules.require,
                  (v) =>
                    recipeUnit?.conversion
                      ? true
                      : 'No conversion found for selected unit',
                ]"
                ref="recipeUnitRef"
                clearable
              >
                <template #prepend-inner>
                  <span v-if="recipeUnit?.conversion" class="d-flex"
                    >{{ recipeUnit?.conversion }}&nbsp; <span>x</span>
                  </span>
                </template>
              </v-autocomplete>
            </v-col>

            <v-col cols="4" v-if="record.itemType == 'made'">
              <v-text-field
                v-model.number="record.prepareQuantity"
                label="Prepare Quantity*"
                type="number"
                variant="outlined"
                density="compact"
                hide-details="auto"
                color="primary"
                min="1"
                :rules="[rules.require, rules.quantity]"
              >
                <template v-slot:append-inner>
                  {{ recipeUnit?.symbol }}
                </template></v-text-field
              >
            </v-col>

            <v-col cols="4">
              <v-text-field
                v-model.number="record.unitCost"
                label="Unit cost*"
                type="number"
                variant="outlined"
                density="compact"
                hide-details="auto"
                color="primary"
                :readonly="record.itemType == 'made'"
              />
            </v-col>

            <v-col cols="4">
              <v-text-field
                v-model.number="record.parLevel"
                label="Par Level"
                type="number"
                variant="outlined"
                density="compact"
                hide-details="auto"
                color="primary"
                @keydown.up.prevent
                @keydown.down.prevent
                :rules="[rules.positive]"
              >
                <template v-slot:append-inner>
                  {{ countingUnit?.symbol }}
                </template>
              </v-text-field>
            </v-col>
            <v-col cols="4">
              <TaxField v-model="record.taxes" return-object multiple />
            </v-col>
            <v-col cols="4">
              <VendorField v-model="record.vendors" return-object multiple />
            </v-col>
            <v-col cols="4">
              <v-text-field
                v-model="record.ledger"
                label="Ledger"
                color="primary"
                hide-details="auto"
                variant="outlined"
                density="compact"
              ></v-text-field>
            </v-col>
            <v-col cols="4">
              <v-text-field
                v-model="record.hsnCode"
                label="HSN Code"
                color="primary"
                hide-details="auto"
                variant="outlined"
                density="compact"
              ></v-text-field>
            </v-col>
            <v-col cols="2" class="py-1">
              <v-switch
                v-model="record.trackExpiry"
                label="Track Expiry"
                color="green"
                hide-details="auto"
                inset=""
              />
            </v-col>
            <v-col cols="2" class="py-1">
              <v-switch
                v-model="record.stockable"
                label="Stockable"
                color="green"
                hide-details="auto"
                inset=""
              />
            </v-col>
            <v-checkbox
              v-model="isEnablePackage"
              color="primary"
              label="  By default, purchases will be processed in purchase units. Select
            this option if you want to create custom packages or brands."
              hide-details
            >
            </v-checkbox>
            <v-col cols="12" v-if="isEnablePackage">
              <h3 class="mb-4">
                <v-icon icon="mdi-package-variant"></v-icon>
                <span class="mx-2">Packages</span>
              </h3>
              <package-table
                v-model="packages"
                :unit="record.purchaseUnit"
              ></package-table>
            </v-col>
            <v-col cols="12" v-if="record.itemType == 'made'">
              <h3 class="mb-4">
                <v-icon icon="mdi-food"></v-icon>
                <span class="mx-2">Ingredients</span>
              </h3>
              <ingredient-table
                v-model="ingredients"
                :inventoryList="filteredInventoryItems"
              ></ingredient-table>
            </v-col>
          </v-row>
        </v-form>
      </v-card-text>
    </v-container>
    <v-container
      v-else
      class="d-flex justify-center align-center"
      style="height: 100%"
    >
      <div class="text-center">
        <v-progress-circular
          color="primary"
          indeterminate
        ></v-progress-circular>
      </div>
    </v-container>
  </div>
</template>
<script setup>
import { ref, onBeforeMount, computed, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import rules from "@/helpers/rules";
import { inventoryRecord as DEFAULT_RECORD } from "@/helpers/defaultRecords";
import { useInventoryItemStore } from "@/stores/inventoryItem";
import { useVendorStore } from "@/stores/vendor";
import { useCategoryStore } from "@/stores/category";
import { useHouseUnitStore } from "@/stores/houseUnit";
import SelectionView from "@/components/base/SelectionView.vue";
import PackageTable from "@/views/inventoryItem/PackageTable.vue";
import IngredientTable from "@/views/inventoryItem/IngredientTable.vue";
import { formatName } from "@/helpers/formatter";
import CurrencyInput from "@/components/CurrencyInput.vue";
import { calculateRecipeCost } from "@/helpers/cost";
import { useTagStore } from "@/stores/tag";
import { useTaxStore } from "@/stores/taxes";
import FormActionsBar from "@/components/utils/FormActionsBar.vue";
import CategoryField from "@/components/fields/CategoryField.vue";
import SubCategoryField from "@/components/fields/SubCategoryField.vue";
import TagField from "@/components/fields/TagField.vue";
import TaxField from "@/components/fields/TaxField.vue";
import VendorField from "@/components/fields/VendorField.vue";

const route = useRoute();
const router = useRouter();
const inventoryItemStore = useInventoryItemStore();
const vendorStore = useVendorStore();
const categoryStore = useCategoryStore();
const houseUnitStore = useHouseUnitStore();
const tagStore = useTagStore();
const taxStore = useTaxStore();

const inventoryItemId = route.params.id;
const isEdit = inventoryItemId !== undefined;

const form = ref(null);
const loader = ref(false);
const isLoading = ref(false);
const subCategories = ref([]);
const isEnablePackage = ref(false);
const inventoryItems = ref([]);

const record = ref(JSON.parse(JSON.stringify(DEFAULT_RECORD)));
const packages = ref([]);
const ingredients = ref([]);

const categories = computed(() => categoryStore.getCategories || []);
const vendors = computed(() => vendorStore.getVendors || []);
const purchaseUnits = computed(() => houseUnitStore.getHouseUnits);


//const inventoryItems = computed(() => inventoryItemStore.getInventoryItems);

const filteredInventoryItems = computed(() => {
  const selectedIds = ingredients.value.map((i) => i.id);
  return inventoryItems.value.filter(
    (item) => !selectedIds.includes(item.id) && item.id !== record.value.id
  );
});

const countingUnit = ref(null);
const recipeUnit = ref(null);
const recipeUnitRef = ref(null);
const countingUnitRef = ref(null);

const handlePurchaseUnitChange = () => {
  countingUnit.value = null;
  recipeUnit.value = null;
  recipeUnitRef.value.resetValidation();
  countingUnitRef.value.resetValidation();
};

// Build a unit map for quick lookup by name
const unitMap = computed(() => {
  const map = new Map();
  purchaseUnits.value.forEach((u) => map.set(u.symbol, u));
  return map;
});

//  Function to calculate conversion from A to B (BFS)
function getConvertedQuantity(fromSymbol, toSymbol) {
  const visited = new Set();
  const queue = [{ symbol: fromSymbol, quantity: 1 }];

  while (queue.length > 0) {
    const { symbol, quantity } = queue.shift();
    if (symbol === toSymbol) return quantity;

    const unit = unitMap.value.get(symbol);
    if (!unit || !unit.conversions) continue;

    for (const conv of unit.conversions) {
      if (visited.has(conv.toUnit)) continue;
      visited.add(conv.toUnit);
      queue.push({ symbol: conv.toUnit, quantity: quantity * conv.quantity });
    }
  }

  return null; // Not found
}

//  Full conversion dropdown with success/failure paths
const countingUnitOptions = computed(() => {
  const fromUnit = record.value.purchaseUnit;
  if (!fromUnit) return [];

  return purchaseUnits.value.map((toUnit) => {
    const qty = getConvertedQuantity(fromUnit.symbol, toUnit.symbol);
    return {
      name: `${toUnit.name} (${toUnit.symbol})`,
      value: toUnit.name,
      conversion: qty ?? null,
      symbol: toUnit.symbol,
    };
  });
});
const onCategoryChange = (val, subCategoryExist) => {
  record.value.category = { id: val.id, name: val.name };
  if (!subCategoryExist) record.value.subCategory = null;
  subCategories.value =
    categories.value.find((item) => item.id == val.id)?.subCategories || [];
};

const handleItemTypeChange = (val) => {
  if (val == "bought") ingredients.value = [];
};

const selectAllStores = computed(
  () => record.value.vendors.length === vendors.value.length
);
const selectSomeStore = computed(
  () => record.value.vendors.length > 0 && !selectAllStores.value
);
const icon = computed(() => {
  if (selectAllStores.value) return "mdi-close";
  if (selectSomeStore.value) return "mdi-minus-box-outline";
  return "mdi-checkbox-blank-outline";
});

const toggle = () => {
  if (selectAllStores.value) return (record.value.vendors = []);
  selectAll();
};

const selectAll = () => {
  record.value.vendors = [];

  if (vendors.value.length) {
    vendors.value.forEach((item) => {
      record.value.vendors.push(item);
    });
  }
};

const toggleTags = () => {
  if (selectAllStores.value) return (record.value.tags = []);
  selectAllTags();
};

const selectAllTags = () => {
  record.value.tags = [];

  if (tags.value.length) {
    tags.value.forEach((item) => {
      record.value.tags.push(item);
    });
  }
};

const toggleTaxes = () => {
  if (selectAllStores.value) return (record.value.taxes = []);
  selectAllTaxes();
};

const selectAllTaxes = () => {
  record.value.taxes = [];

  if (taxes.value.length) {
    taxes.value.forEach((item) => {
      record.value.taxes.push(item);
    });
  }
};

onBeforeMount(async () => {
  loader.value = true;
  try {
    const houseUnitPromise = houseUnitStore.fetchHouseUnitsBytenant();
    await Promise.all([
      houseUnitPromise,
    ]);

    if (isEdit) {
      const result = await inventoryItemStore.fetchInventoryItemById(
        inventoryItemId
      );
      record.value = result;
      countingUnit.value = result.countingUnit;
      recipeUnit.value = result.recipeUnit;
      onCategoryChange(record.value.category, true);
    }
    packages.value = record.value.packages;
    isEnablePackage.value = packages.value.length > 0;
    ingredients.value =
      record.value.ingredients?.map((v) => ({
        ...v,
        name: v.itemName,
        id: v.itemId,
        code: v.itemCode,
      })) || [];
    loader.value = false;
  } catch (error) {
    console.log(error);
  }
});

const save = async () => {
  if (isLoading.value) return;
  isLoading.value = true;

  try {
    const { valid } = await form.value.validate();
    if (!valid) {
      isLoading.value = false;
      return;
    }

    const payload = {
      ...record.value,
      purchaseUnit: {
        id: record.value.purchaseUnit.id,
        name: record.value.purchaseUnit.name,
        symbol: record.value.purchaseUnit.symbol,
      },
      subCategory: { id: record.value.subCategory.id, name: record.value.subCategory.name },
      tags: (record.value.tags || [])
        .filter((t) => t && (typeof t === "string" || t.id))
        .map((t) => (typeof t === "string" ? t : t.id)),
        taxes: (record.value.taxes ?? []).map((t) => ({
          id: t.id,
          name: t.name,
        })),
      packages: isEnablePackage.value ? packages.value : [],
      ingredients: ingredients.value?.map((v) => ({
        itemId: v.id,
        itemName: v.name,
        itemCode: v.code,
        quantity: v.quantity,
        purchaseUnit: v.purchaseUnit,
        countingUnit: v.countingUnit,
        recipeUnit: v.recipeUnit,
        unitCost: v.unitCost,
        hsnCode: v.hsnCode,
        // symbol: v.symbol,
        // recipeConversion: v.recipeConversion,
      })),
      countingUnit: {
        symbol: countingUnit.value.symbol,
        conversion: countingUnit.value.conversion,
        name: countingUnit.value.name,
      },
      recipeUnit: {
        symbol: recipeUnit.value.symbol,
        conversion: recipeUnit.value.conversion,
        name: recipeUnit.value.name,
      },
    };

    let itemId;
    if (isEdit) {
      await inventoryItemStore.updateInventoryItem(payload);
    } else {
      const createdItem = await inventoryItemStore.createInventoryItem(payload);
      itemId = createdItem?.id;
    }

    const redirect = (name, id, type, accountId) => {
      router.push({
        name,
        params: { id },
        query: {
          createdItemId: itemId,
          createdItemType: type,
          accountId: accountId,
        },
      });
    };

    const { menuItemId, modifierId, menuItemType, modifierType, accountId } =
      route.query;

    if (menuItemId) {
      return redirect("edit-menu-item", menuItemId, menuItemType, accountId);
    }

    if (modifierId) {
      return redirect("edit-modifier", modifierId, modifierType, accountId);
    }

    navigatePrevious();
  } catch (error) {
    console.error(error);
  } finally {
    isLoading.value = false;
  }
};

const navigatePrevious = () => {
  const { menuItemId, modifierId, accountId } = route.query;

  if (menuItemId) {
    return router.push({
      name: "edit-menu-item",
      params: { id: menuItemId },
      query: { accountId },
    });
  }

  if (modifierId) {
    return router.push({
      name: "edit-modifier",
      params: { id: modifierId },
      query: { accountId },
    });
  }

  router.push({ name: "inventory-items" });
};

const cleanName = () => {
  record.value.itemName = formatName(record.value.itemName);
};

const totalCost = computed(() => {
  if (record.value.itemType == "bought" || !ingredients.value.length) return 0;
  return ingredients.value.reduce(
    (acc, item) => acc + calculateRecipeCost(item, item.quantity),
    0
  );
});

const tags = computed(() =>
  tagStore.getTags
    .filter((tag) => tag.activeStatus)
    .map((tag) => ({ id: tag.id, name: tag.name }))
);

const taxes = computed(() =>
  taxStore.getTaxes
    .filter((tax) => tax.activeStatus)
    .map((tax) => ({ id: tax.id, name: tax.name }))
);

watch(totalCost, (v) => {
  // if (record.value.itemType == "bought" || !ingredients.value.length) return;
  record.value.unitCost = v;
});

watch(
  () => record.value.itemType,
  async (newType) => {
    if (newType === "made") {
      // Only fetch when needed
      if (!inventoryItemStore.getInventoryItems.length) {
        await inventoryItemStore.fetchInventoryItems(); 
      }
      inventoryItems.value = inventoryItemStore.getInventoryItems;
    } else {
      // Clear if switching back to "bought"
      inventoryItems.value = [];
    }
  },
  { immediate: false }
);

</script>

<style scoped></style>
