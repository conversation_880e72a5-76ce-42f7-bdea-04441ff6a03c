<template>
  <div>
    <!-- List Actions Bar -->
    <list-actions-bar
      search-label="Search GRN"
      :hideImportExport="true"
      :hide-filter="true"
      :hide-add="true"
      @search="handleSearch"
      @refresh="refresh"
      @export="(type) => console.log(type)"
    >
      <template #prepend-actions>
        <v-btn
          variant="tonal"
          color="primary"
          prepend-icon="mdi-filter-variant"
          class="ml-2"
          @click="toggleFilter"
        >
          <span class="d-none d-md-flex">Filters</span>
        </v-btn>
      </template>
    </list-actions-bar>

    <!-- Main content -->
    <v-container fluid>
      <v-row no-gutters>
        <v-card border rounded="lg" width="100%">
          <v-data-table
            class="table-bordered"
            :headers="grnHeaders"
            :items="filteredItems"
            :loading="loading"
            :sort-by="sortBy"
            :hide-default-footer="filteredItems.length < 11"
            no-data-text="No Grn found"
          >
            <!-- Loading Skeleton -->
            <template #loading>
              <v-skeleton-loader type="table-row@10"></v-skeleton-loader>
            </template>

            <!-- Loader -->
            <template #loader>
              <v-progress-linear
                :height="2"
                indeterminate
                color="primary"
              ></v-progress-linear>
            </template>

            <template #item.grnNumber="{ item }">
              <div class="d-flex align-center ga-2">
                <span
                  class="text-decoration-underline cursor-pointer"
                  @click="goToViewPage(item.id)"
                >
                  {{ item.grnNumber }}
                </span>

                <!-- attachment icon button -->
                <!-- attachment icon button -->
                <v-icon
                  v-if="item.attachments && item.attachments.length > 0"
                  icon="mdi-attachment"
                  color="success"
                  @click.stop="showAttachments(item)"
                ></v-icon>
                <v-icon
                  v-else
                  icon="mdi-attachment-off"
                  color="error"
                  @click.stop="showAttachments(item)"
                ></v-icon>
              </div>
            </template>

            <template #item.totalValue="{ item }">
              <span>{{ item.totalValue.toFixed(2) }}</span>
            </template>

            <template #item.status="{ item }">
              <v-chip
                class="text-uppercase"
                label
                :color="getStatusColor(item.status)"
              >
                {{ item.status }}
              </v-chip>
            </template>

            <template #item.action="{ item }">
              <GRNOptions
                icon
                status="completed"
                :itemId="item.id"
                @refresh="refresh"
                :filters="dataFilters"
              />
            </template>
          </v-data-table>
        </v-card>
      </v-row>
    </v-container>
    <filter-nav-drawer
      ref="editFilter"
      :tabs="tabs"
      :filtersData="filters"
      @apply-filter="applyFilters"
    ></filter-nav-drawer>

    <!-- attachment dialog -->
    <AttachmentDialog
      v-model="showAttachmentsDialog"
      v-if="selectedItem"
      type="grn"
      :item-id="selectedItem.id"
      :update-db-url="`purchases/grn/${selectedItem.id}/attachments`"
      :attachments="selectedItem.attachments"
      add
      @uploaded="onUploaded"
      @deleted="onDeleted"
    />
  </div>
</template>

<script setup>
import { onBeforeMount, ref, computed } from "vue";
import ListActionsBar from "@/components/utils/ListActionsBar.vue";
import AutoComplete from "@/components/filters/Autocomplete.vue";
import FilterNavDrawer from "@/components/filters/NavDrawer.vue";
import GRNOptions from "@/components/purchase/GRNOptions.vue";

import { grnHeaders } from "@/helpers/tableHeaders";
import { useRouter } from "vue-router";

import { useLocationStore } from "@/stores/location";
import { useStoreStore } from "@/stores/store";
import { useGrnStore } from "@/stores/goodsReceivedNote";
import { getStatusColor } from "@/helpers/status";
import { purchaseStatus } from "@/constants/status";
import AttachmentDialog from "@/components/attach/AttachmentDialog.vue";

const router = useRouter();
const grnStore = useGrnStore();
const locationStore = useStoreStore();
const workAreaStore = useLocationStore();

const locations = computed(() => locationStore.getStores || []);
const workAreas = computed(() => workAreaStore.getLocations || []);

const search = ref(null);
const loading = ref(false);
const sortBy = ref([{ key: "grnNumber", order: "desc" }]);

const items = computed(() => grnStore.getGrnList || []);

const dataFilters = ref({});
const tabs = [{ value: 1, label: "filters" }];
const filters = computed(() => [
  {
    component: AutoComplete,
    title: "Location",
    key: "locations",
    items: locations.value,
    default: true,
  },
  {
    component: AutoComplete,
    title: "WorkArea/Storage",
    key: "inventoryLocations",
    items: workAreas.value,
  },
]);

const applyFilters = (filters) => {
  dataFilters.value = filters;
  refresh();
};

const filteredItems = computed(() => {
  if (!search.value) return items.value;
  const query = search.value.toLowerCase();
  return items.value.filter((item) =>
    item.grnNumber.toLowerCase().includes(query)
  );
});
const handleSearch = (v) => {
  search.value = v;
};

const goToViewPage = (id) => {
  router.push({ name: "view-grn", params: { id } });
};

const refresh = async () => {
  try {
    loading.value = true;
    search.value = null;
    await grnStore.fetchGrnList(dataFilters.value);
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
};

const editFilter = ref(null);
const toggleFilter = () => {
  editFilter.value.toggle();
};

onBeforeMount(async () => {
  try {
    loading.value = true;
    await Promise.all([
      locationStore.fetchStores(),
      workAreaStore.fetchLocations(),
    ]);
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
});

const showAttachmentsDialog = ref(false);
const selectedItem = ref(null);

const showAttachments = (item) => {
  selectedItem.value = item;
  showAttachmentsDialog.value = true;
};

const onUploaded = async () => {
  showAttachmentsDialog.value = false;
  refresh();
};

const onDeleted = async () => {
  refresh();
};
</script>
