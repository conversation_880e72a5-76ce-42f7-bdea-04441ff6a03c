<template>
  <form-actions-bar
    @close="navigatePrevious"
    @submit="save"
    :loading="loading"
  />
  <v-container fluid v-if="!loading">
    <v-card flat>
      <v-card-text>
        <v-form ref="form">
          <v-row>
            <v-col cols="12" sm="6" md="4" xl="3">
              <location-field v-model="record.location" mandatory />
            </v-col>
            <v-col cols="12" sm="6" md="4" xl="3">
              <work-area-field
                v-model="record.inventoryLocation"
                :locations="record.location"
                mandatory
              />
            </v-col>
            <v-col cols="12">
              <spoilage-table
                v-if="record.location && record.inventoryLocation"
                v-model="items"
                :itemList="filteredInventoryItems"
              ></spoilage-table>
            </v-col>
          </v-row>
        </v-form> </v-card-text
    ></v-card>
  </v-container>
  <page-loader v-else></page-loader>
</template>
<script setup>
import { ref, computed, onBeforeMount } from "vue";
import { useRouter } from "vue-router";
import { adjustmentRecord as DEFAULT_RECORD } from "@/helpers/defaultRecords";

// components
import FormActionsBar from "@/components/utils/FormActionsBar.vue";
import PageLoader from "@/components/utils/PageLoader.vue";
import LocationField from "@/components/fields/LocationField.vue";
import WorkAreaField from "@/components/fields/WorkAreaField.vue";
import SpoilageTable from "@/components/tables/SpoilageTable.vue";

// store
import { useInventoryItemStore } from "@/stores/inventoryItem";
import { useSpoilageStore } from "@/stores/spoilage";
import { useStoreStore } from "@/stores/store";
import { useLocationStore } from "@/stores/location";

const router = useRouter();

const inventoryItemStore = useInventoryItemStore();
const spoilageStore = useSpoilageStore();
const locationStore = useStoreStore();
const workAreaStore = useLocationStore();

const loading = ref(false);

const form = ref(null);
const record = ref(JSON.parse(JSON.stringify(DEFAULT_RECORD)));
const items = ref([]);

const filteredInventoryItems = computed(() =>
  inventoryItemStore.getInventoryItems.filter((item) => {
    const selecteditemIds = items.value.map((i) => i.itemId);
    return !selecteditemIds.includes(item.id);
  })
);

const locations = computed(() => locationStore.getStores || []);
const workAreas = computed(() => workAreaStore.getLocations || []);

const save = async () => {
  if (loading.value) return;
  loading.value = true;
  try {
    // const { valid } = await form.value.validate();
    // console.log(valid, "valid");
    // if (!valid) {
    //   loading.value = false;
    //   return;
    // }
    const { id: locationId, name: locationName } = locations.value.find(
      (loc) => loc.id === record.value.location
    );

    const { id: workAreaId, name: workAreaName } = workAreas.value.find(
      (loc) => loc.id === record.value.inventoryLocation
    );

    const payload = {
      location: {
        id: locationId,
        name: locationName,
      },
      inventoryLocation: {
        id: workAreaId,
        name: workAreaName,
      },
      items: items.value,
    };
    await spoilageStore.createSpoilage(payload);
    navigatePrevious();
  } catch (error) {
  } finally {
    loading.value = false;
  }
};

onBeforeMount(async () => {
  loading.value = true;
  try {
    await inventoryItemStore.fetchInventoryItems();
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
});

const navigatePrevious = () => {
  router.push({
    name: "spoilage",
  });
};
</script>
