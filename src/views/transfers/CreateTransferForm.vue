<template>
  <div style="height: 100%">
    <page-loader v-if="loading" />

    <!-- Form Actions Bar -->
    <form-actions-bar
      v-if="!loading"
      @close="navigatePrevious"
      :hide-submit="true"
      :loading="loading"
    >
      <template #actions>
        <v-btn
          text="Create"
          @click="create"
          variant="flat"
          color="primary"
          class="ml-2"
          :loading="loading"
          :disabled="loading"
        />
      </template>
    </form-actions-bar>

    <v-container v-if="!loading" fluid>
      <v-form ref="form" v-model="formValid">
        <v-row>
          <!-- Transfer Type -->
          <v-col cols="12" sm="6" md="3">
            <v-select
              ref="transferType"
              v-model="record.transferType"
              :items="transferTypes"
              item-value="value"
              item-title="text"
              label="Transfer Type*"
              variant="outlined"
              density="compact"
              color="primary"
              :rules="[rules.require]"
              @update:model-value="onTransferTypeChange"
            />
          </v-col>

          <!-- From Location -->
          <v-col cols="12" sm="6" md="3">
            <location-field
              v-model="fromLocation"
              label="From Location"
              hint="Dispatch Location"
              persistent-hint
              mandatory
              :exclude="toLocation ? toLocation.id : null"
              :noAuth="record.transferType === 'external'"
              return-object
              @update:model-value="onFromLocationChange"
            />
          </v-col>

          <!-- From WorkArea -->
          <v-col cols="12" sm="6" md="3">
            <work-area-field
              ref="fromRef"
              v-model="issuerWorkArea"
              label="From WorkArea"
              hint="Dispatch WorkArea"
              persistent-hint
              mandatory
              return-object
              :locations="fromLocation ? [fromLocation.id] : null"
              :exclude="requesterWorkArea ? requesterWorkArea.id : null"
              :disabled="!fromLocation"
              @update:model-value="onFromWorkAreaChange"
            />
          </v-col>

          <!-- To Location (External only) -->
          <v-col
            cols="12"
            sm="6"
            md="3"
            v-if="record.transferType === 'external'"
          >
            <location-field
              v-model="toLocation"
              label="To Location"
              hint="Receive Location"
              persistent-hint
              mandatory
              :exclude="fromLocation ? fromLocation.id : null"
              return-object
              :disabled="!fromLocation"
              @update:model-value="onToLocationChange"
            />
          </v-col>

          <!-- To WorkArea -->
          <v-col cols="12" sm="6" md="3">
            <work-area-field
              ref="toRef"
              v-model="requesterWorkArea"
              label="To WorkArea"
              hint="Receive WorkArea"
              persistent-hint
              mandatory
              return-object
              :locations="requesterWorkAreaFilter"
              :exclude="issuerWorkArea ? issuerWorkArea.id : null"
              :disabled="
                !fromLocation ||
                (!issuerWorkArea && record.transferType === 'internal') ||
                !requesterWorkAreaFilter.length
              "
            />
          </v-col>
        </v-row>
      </v-form>

      <!-- Table -->
      <transfer-table
        :transferList="tableItems"
        :formValid="formValid"
        @addItem="openForm = true"
        @removeItem="remove"
      />

      <transfer-item-form
        v-if="fromLocation"
        v-model="openForm"
        :formValid="formValid"
        :locationId="fromLocation ? fromLocation.id : null"
        @add="add"
        :existingItems="tableItems"
      />
    </v-container>
  </div>
</template>

<script setup>
import { ref, computed, onBeforeMount, onMounted, nextTick } from "vue";
import { useRouter } from "vue-router";
import { indentRequestRecord as DEFAULT_RECORD } from "@/helpers/defaultRecords";
import rules from "@/helpers/rules";
import { useTransferStore } from "@/stores/transfer";
import { useSnackbarStore } from "@/stores/snackBar";
import TransferTable from "@/views/transfers/TransferTable.vue";
import FormActionsBar from "@/components/utils/FormActionsBar.vue";
import PageLoader from "@/components/utils/PageLoader.vue";
import WorkAreaField from "@/components/fields/WorkAreaField.vue";
import LocationField from "@/components/fields/LocationField.vue";
import TransferItemForm from "@/views/transfers/TransferItemForm.vue";

const router = useRouter();
const { showSnackbar } = useSnackbarStore();
const transferStore = useTransferStore();

const record = ref({ ...DEFAULT_RECORD });
const openForm = ref(false);
const form = ref(null);
const formValid = ref(false);
const loading = ref(false);
const transferType = ref(null);

const fromLocation = ref(null);
const toLocation = ref(null);
const issuerWorkArea = ref(null);
const requesterWorkArea = ref(null);

const fromRef = ref(null);
const toRef = ref(null);

const tableItems = ref([]);

const transferTypes = [
  { value: "internal", text: "Internal" },
  { value: "external", text: "External" },
];

const add = (pr) => {
  tableItems.value.unshift(pr);
};

const remove = (index) => {
  tableItems.value.splice(index, 1);
};

const requesterWorkAreaFilter = computed(() => {
  if (record.value.transferType === "internal" && fromLocation.value) {
    return [fromLocation.value.id];
  }
  if (record.value.transferType === "external" && toLocation.value) {
    return [toLocation.value.id];
  }
  return [];
});

const create = async () => {
  const { valid } = await form.value.validate();
  if (!valid) return;

  if (!tableItems.value.length) {
    showSnackbar("primary", "At least one item is required");
    return;
  }

  loading.value = true;
  try {
    const payload = {
      requester: {
        id: requesterWorkArea.value.id,
        name: requesterWorkArea.value.name,
        locationId: requesterWorkArea.value.locationId,
        locationName: requesterWorkArea.value.locationName,
      },
      issuer: {
        id: issuerWorkArea.value.id,
        name: issuerWorkArea.value.name,
        locationId: issuerWorkArea.value.locationId,
        locationName: issuerWorkArea.value.locationName,
      },
      items: tableItems.value.map((i) => ({
        itemId: i.itemId,
        itemName: i.itemName,
        itemCode: i.itemCode,
        categoryId: i.categoryId,
        subcategoryId: i.subCategoryId,
        categoryName: i.categoryName,
        subcategoryName: i.subCategoryName,
        requestedQuantity: i.requestedQuantity,
        countingUOM: i.countingUOM,
        pkg: {
          ...i.pkg,
          id: i.pkg?.id ? i.pkg.id : "default",
          name: i.pkg?.name ? i.pkg.name : "Default",
        },
      })),
    };

    await transferStore.createTransfer(payload);
    navigatePrevious();
  } catch (err) {
    console.error(err);
  } finally {
    loading.value = false;
  }
};

const navigatePrevious = () => router.push({ name: "transfers" });

onBeforeMount(async () => {
  record.value.transferType = "internal";
});

const onTransferTypeChange = () => {
  issuerWorkArea.value = null;
  requesterWorkArea.value = null;
  toLocation.value = null;
  fromLocation.value = null;
};

const onFromLocationChange = (loc) => {
  issuerWorkArea.value = null;
  requesterWorkArea.value = null;
  toLocation.value = null;
  if (record.value.transferType === "internal") {
    toLocation.value = loc;
  }
  if (loc && fromRef.value) {
    fromRef.value.setDefault(loc.inventoryLocationId);
  }
};

const onToLocationChange = (loc) => {
  requesterWorkArea.value = null;
  if (loc && toRef.value) {
    toRef.value.setDefault(loc.inventoryLocationId);
  }
};

const onFromWorkAreaChange = () => {
  if (record.value.transferType === "internal") {
    requesterWorkArea.value = null;
  }
}

let hasFocusedInitially = false;

const focusFirstField = () => {
  if (transferType.value?.$el) {
    const input = transferType.value.$el.querySelector('input, .v-input input');
    if (input) input.focus();
  }
};

onMounted(async () => {
  await nextTick();
  focusFirstField();
  hasFocusedInitially = true;

  window.addEventListener('keydown', (e) => {
    if (e.key === 'Tab' && document.activeElement === document.body) {
      e.preventDefault();
      focusFirstField();
    }
  });
});

</script>
