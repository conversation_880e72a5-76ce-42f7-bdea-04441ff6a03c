<template>
  <div style="height: 100%">
    <page-loader v-if="loading" />

    <!-- Form Actions Bar -->
    <form-actions-bar
      v-if="!loading"
      @close="navigatePrevious"
      :hide-submit="true"
      :loading="loading"
    >
      <template #actions>
        <!-- <v-btn
          text="Export"
          @click="exportTemplate"
          variant="flat"
          :loading="loading"
          :disabled="loading"
          color="primary"
          class="ml-2"
        />

        <v-btn
          text="Import"
          @click="importTemplate"
          variant="flat"
          :loading="loading"
          :disabled="loading"
          color="primary"
          class="ml-2"
        /> -->

        <v-btn
          :text="'Create'"
          @click="create"
          variant="flat"
          color="primary"
          class="ml-2"
          :loading="loading"
          :disabled="loading"
        />
      </template>
    </form-actions-bar>

    <v-container v-if="!loading" fluid>
      <v-form ref="form">
        <v-row>
          <v-col cols="12" sm="6" md="3">
            <work-area-field
              v-model="selectedWorkArea"
              label="Select workarea for closing"
              mandatory
            />
          </v-col>
          <v-col cols="12" sm="6" md="3">
            <v-date-input
              v-model="deliveryDate"
              label="Closing Date"
              color="primary"
              class="mb-2"
              :min="today"
              variant="outlined"
              density="compact"
              hide-details="auto"
              prepend-icon
              prepend-inner-icon="$calendar"
              hide-actions
            ></v-date-input>
          </v-col>
        </v-row>
      </v-form>

      <closing-dialog v-if="dialog" v-model="dialog" @selectOption="submit"></closing-dialog>

      <closing-table v-model="closingItems" :inventoryList="filteredInventoryItems"></closing-table>
    </v-container>
  </div>
</template>

<script setup>
import { onBeforeMount, ref, computed, watch } from "vue";
import { useRouter } from "vue-router";
import { useClosingStore } from "@/stores/closing";
import { useLocationStore } from "@/stores/location";
import ClosingDialog from "./ClosingDialog.vue";
import { useSnackbarStore } from "@/stores/snackBar";
import FormActionsBar from "@/components/utils/FormActionsBar.vue";
import PageLoader from "@/components/utils/PageLoader.vue";
import WorkAreaField from "@/components/fields/WorkAreaField.vue";
import ClosingTable from "./ClosingTable.vue";
import * as XLSX from "xlsx";
import { createClosingHeaders } from "@/helpers/tableHeaders";

const { showSnackbar } = useSnackbarStore();

const closingStore = useClosingStore();
const locationStore = useLocationStore();

const router = useRouter();

const inventoryItems = computed(() => closingStore.getClosingItems);
const locations = computed(() => locationStore.getLocations);

const form = ref(null);

const deliveryDate = ref(null);
const loading = ref(false);

const selectedWorkArea = ref(null);
const selectedLocation = ref(null);

const dialog = ref(false);
const closingItems = ref([]);
const closingItemsData = ref([]);
const today = new Date().toISOString().split("T")[0];

const navigatePrevious = () => {
  router.push({ name: "closing" });
};

const filteredInventoryItems = computed(() => {
  const selectedIds = closingItems.value.map(i => i.id);
  return inventoryItems.value.filter(item => !selectedIds.includes(item.id));
});

const create = async () => {
  const { valid } = await form.value.validate();
  if (!valid) return;

  if (!closingItems.value.length) {
    showSnackbar("primary", "At least one item is required");
    return;
  }
  dialog.value = true;
};

const submit = async option => {
  loading.value = true;
  try {
    const payload = {
      locationId: selectedLocation.value?.locationId,
      locationName: selectedLocation.value?.locationName,
      workAreaId: selectedWorkArea.value,
      workAreaName: selectedLocation.value?.name,
      stockCorrection: option === 1 ? true : false,
      closingDate: deliveryDate.value,
      items: closingItems.value.map(ing => ({
        itemId: ing.id,
        itemName: ing.name,
        itemType: ing.itemType,
        itemCode: ing.itemCode,
        countingUOM: ing.countingUnit.symbol,
        categoryId: ing.category.id,
        subcategoryId: ing.subCategory.id,
        closingQuantity: ing.quantity
      }))
    };
    await closingStore.createClosingData(payload);
    navigatePrevious();
  } catch (err) {
    console.error(err);
    showSnackbar("error", "Failed to create closing");
  } finally {
    loading.value = false;
  }
};

const exportTemplate = async () => {
  try {
    loading.value = true;

    // Filter out unwanted columns like "Actions"
    const exportableHeaders = createClosingHeaders.filter(
      h => h.title.toLowerCase() !== "actions"
    );

    // Explicitly define the order of data keys you want in Excel
    const headers = exportableHeaders.map(h => h.title);
    const keys = exportableHeaders.map(h => h.key);

    // Build data rows
    const data = closingItemsData.value.map(ing => ({
      name: ing.itemName,
      itemCode: ing.itemCode,
      category: ing.category?.name,
      subCategory: ing.subCategory?.name,
      countingUnit: ing.countingUnit?.symbol,
      quantity: 0 // Default quantity
    }));

    // Combine headers and data
    const worksheetData = [headers];
    data.forEach(item => worksheetData.push(keys.map(k => item[k] ?? "")));

    // Create worksheet & workbook
    const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "Closing Template");

    // Auto-adjust column width (optional)
    // worksheet["!cols"] = headers.map(() => ({ wch: 20 }));

    // Download file
    XLSX.writeFile(workbook, "Closing_Template.xlsx");

    showSnackbar("success", "Template exported successfully!");
  } catch (error) {
    console.error(error);
    showSnackbar("error", "Failed to export template");
  } finally {
    loading.value = false;
  }
};

const importTemplate = async () => {
  try {
    // Create hidden file input
    const input = document.createElement("input");
    input.type = "file";
    input.accept = ".xlsx, .xls";

    input.onchange = async e => {
      const file = e.target.files?.[0];
      if (!file) return;

      loading.value = true;
      try {
        // Read Excel file
        const data = await file.arrayBuffer();
        const workbook = XLSX.read(data, { type: "array" });
        const sheet = workbook.Sheets[workbook.SheetNames[0]];
        const json = XLSX.utils.sheet_to_json(sheet, { defval: "" });

        if (!json.length) {
          showSnackbar("warning", "No data found in the uploaded file");
          return;
        }

        // Run validation
        const validationResult = validateImportedData(
          json,
          inventoryItems.value
        );

        if (!validationResult.isValid) {
          showSnackbar("error", validationResult.message);
          return;
        }

        // Map valid data
        closingItems.value = json.map(row => ({
          name: row["Item Name"],
          itemName: row["Item Name"],
          itemCode: row["Item Code"],
          category: { name: row["Category"] },
          subCategory: { name: row["Sub Category"] },
          countingUnit: { symbol: row["Counting Unit"] },
          quantity: Number(row["Quantity"] || 0)
        }));

        showSnackbar("success", "Template imported successfully!");
      } catch (err) {
        console.error("Import error:", err);
        showSnackbar("error", "Failed to import template");
      } finally {
        loading.value = false;
      }
    };

    input.click();
  } catch (error) {
    console.error("Unexpected error:", error);
    showSnackbar("error", "Import failed");
  }
};

const validateImportedData = (json, inventoryItems = []) => {
  if (!json.length) return { isValid: false, message: "No data to validate" };

  const validKeys = new Set(
    inventoryItems.map(
      inv =>
        `${inv.itemName
          ?.trim()
          .toLowerCase()}|${inv.itemCode?.trim().toLowerCase()}`
    )
  );

  const requiredFields = [
    "Item Name",
    "Item Code",
    "Category",
    "Sub Category",
    "Counting Unit"
  ];

  const seenKeys = new Set();
  const issues = {
    empty: [],
    invalid: [],
    duplicate: []
  };

  for (let i = 0; i < json.length; i++) {
    const row = json[i];
    const rowNo = i + 2;

    // Check for missing required fields
    if (requiredFields.some(f => !row[f]?.trim())) {
      issues.empty.push(rowNo);
      continue;
    }

    const name = row["Item Name"]?.trim().toLowerCase();
    const code = row["Item Code"]?.trim().toLowerCase();
    const key = `${name}|${code}`;

    // Validate existence
    if (!validKeys.has(key)) {
      issues.invalid.push(rowNo);
      continue;
    }

    // Check duplicates
    if (seenKeys.has(key)) {
      issues.duplicate.push(rowNo);
    } else {
      seenKeys.add(key);
    }
  }

  if (issues.empty.length)
    return {
      isValid: false,
      message: `Empty rows found at: ${issues.empty.join(", ")}`
    };

  if (issues.invalid.length)
    return {
      isValid: false,
      message: `Items in these rows are not in closing data: ${issues.invalid.join(
        ", "
      )}`
    };

  if (issues.duplicate.length)
    return {
      isValid: false,
      message: `Duplicate rows found at: ${issues.duplicate.join(", ")}`
    };

  return { isValid: true };
};

onBeforeMount(async () => {
  deliveryDate.value = deliveryDate.value ? deliveryDate.value : new Date();
  await locationStore.fetchLocations();
});

watch(
  selectedWorkArea,
  async newValue => {
    if (!newValue) return;

    try {
      await closingStore.fetchClosingItems({ locationId: newValue });
      selectedLocation.value = locations.value.find(loc => loc.id === newValue);
      closingItemsData.value = closingStore.getClosingItems;
    } catch (error) {
      console.error(error);
    }
  },
  { immediate: true }
);
</script>

<style scoped>
</style>
