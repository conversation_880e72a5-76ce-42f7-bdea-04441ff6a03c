<template>
  <v-container fluid class="pa-0">
    <v-row no-gutters>
      <v-card border rounded="lg" class="my-2" width="100%">
        <v-data-table
          class="table-bordered"
          :items="items"
          :headers="headers"
          items-per-page="-1"
          :sort-by="sortBy"
        >
          <!-- Add New Row -->
          <template #body.prepend>
            <tr>
              <td>
                <v-autocomplete
                  v-model="newRow.name"
                  :items="inventoryList"
                  item-title="itemName"
                  item-value="id"
                  density="compact"
                  variant="outlined"
                  single-line
                  hide-details
                  color="primary"
                  class="py-4"
                  return-object
                  ref="inputRef"
                  clearable
                  @update:model-value="(val) => setValues(val, null)"
                />
              </td>

              <td class="text-center">{{ newRow.itemCode || '-' }}</td>
              <td class="text-center">{{ newRow.category?.name || '-' }}</td>
              <td class="text-center">{{ newRow.subCategory?.name || '-' }}</td>
              <td class="text-center">{{ newRow.countingUnit?.symbol || '-' }}</td>

              <td>
                <v-text-field
                  v-model="newRow.quantity"
                  type="number"
                  density="compact"
                  variant="outlined"
                  hide-details
                  color="primary"
                  @keydown.up.prevent
                  @keydown.down.prevent
                  @keypress.enter="addRow(newRow)"
                  @keypress="preventKeys"
                  :rules="[rules.positive]"
                >
                  <template #append-inner>
                    {{ newRow.countingUnit?.symbol }}
                  </template>
                </v-text-field>
              </td>

              <td class="text-center">
                <v-icon
                  color="green"
                  :disabled="!isRowValid(newRow)"
                  @click="commitNewRow"
                >
                  mdi-plus
                </v-icon>
              </td>
            </tr>
          </template>

          <!-- Existing Rows -->
          <template #item.name="{ item }">
            <v-autocomplete
              v-model="item.name"
              :items="inventoryList"
              item-title="itemName"
              item-value="id"
              density="compact"
              variant="outlined"
              single-line
              hide-details
              color="primary"
              class="py-4"
              :rules="[rules.require]"
              return-object
              @update:model-value="(val) => setValues(val, item)"
              clearable
            />
          </template>

          <template #item.itemCode="{ item }">{{ item.itemCode }}</template>
          <template #item.category="{ item }">{{ item.category?.name }}</template>
          <template #item.subCategory="{ item }">{{ item.subCategory?.name }}</template>
          <template #item.countingUnit="{ item }">{{ item.countingUnit?.symbol }}</template>

          <template #item.quantity="{ item }">
            <v-text-field
              v-model="item.quantity"
              type="number"
              density="compact"
              variant="outlined"
              hide-details
              color="primary"
              :rules="[rules.require, rules.positive]"
              @keydown.up.prevent
              @keydown.down.prevent
              @keypress="preventKeys"
            >
              <template #append-inner>
                {{ item.countingUnit?.symbol }}
              </template>
            </v-text-field>
          </template>

          <template #item.actions="{ index }">
            <v-icon color="error" @click="removeRow(index)">
              mdi-close
            </v-icon>
          </template>
        </v-data-table>
      </v-card>
    </v-row>
  </v-container>
</template>

<script setup>
import { ref, nextTick } from "vue";
import rules from "@/helpers/rules";
import { createClosingHeaders } from "@/helpers/tableHeaders";
import { closingRecord as DEFAULT_RECORD } from "@/helpers/defaultRecords";

const props = defineProps({
  inventoryList: {
    type: Array,
    default: () => [],
  },
});

const items = defineModel();
const inputRef = ref(null);
const headers = createClosingHeaders;
const sortBy = ref([{ key: "itemName", order: "asc" }]);

const newRow = ref({ ...DEFAULT_RECORD });

const preventKeys = (event) => {
  if (["-", "+", "e"].includes(event.key)) event.preventDefault();
};

const isRowValid = (row) => {
  return !!(row.name && row.quantity > 0);
};

const commitNewRow = () => {
  if (!isRowValid(newRow.value)) return;
  items.value.push({ ...newRow.value });
  newRow.value = { ...DEFAULT_RECORD };
  nextTick(() => inputRef.value?.focus?.());
};

const addRow = (row) => {
  if (!isRowValid(row)) return;
  commitNewRow();
};

const removeRow = (index) => {
  items.value.splice(index, 1);
};

const setValues = (val, row = null) => {  
  if (!val) return;
  const target = row || newRow.value;

  target.name = val.itemName;
  target.id = val.id;
  target.itemType = val.itemType;
  target.itemCode = val.itemCode;
  target.countingUnit = val.countingUnit;
  target.category = val.category;
  target.subCategory = val.subCategory;
};
</script>


<style scoped>
</style>
