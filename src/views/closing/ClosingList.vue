<template>
  <div>
    <list-actions-bar
      search-label="Search Closing"
      add-label="Closing"
      :hideImportExport="true"
      :hide-filter="true"
      @search="handleSearch"
      @refresh="refresh"
      @add="add"
    >
      <template #prepend-actions>
        <v-btn
          variant="tonal"
          color="primary"
          prepend-icon="mdi-filter-variant"
          class="ml-2"
          @click="toggleFilter"
        >
          <span class="d-none d-md-flex">Filters</span>
        </v-btn>
      </template>
    </list-actions-bar>

    <v-container fluid>
      <v-row no-gutters>
        <v-card border rounded="lg"  width="100%">
          <v-data-table
            class="table-bordered"
            :headers="headers"
            :items="filteredItems"
            :loading="loading"
            :sort-by="sortBy"
            :hide-default-footer="filteredItems.length < 11"
            no-data-text="No closing items found"
          >
            <!-- Loading Skeleton -->
            <template v-slot:loading>
              <v-skeleton-loader type="table-row@10"></v-skeleton-loader>
            </template>
            <!-- Loader -->
            <template v-slot:loader>
              <v-progress-linear :height="2" indeterminate color="primary"></v-progress-linear>
            </template>

            <!-- content -->     
            <template #item.closingDate="{ item }">{{ ConvertIOStoDate(item.closingDate) }}</template>
            <template #item.createdDate="{ item }">{{ ConvertIOStoDate(item.createdDate) }}</template>
            <template #item.createdTime="{ item }">{{ ConvertIOStoTime(item.createdDate) }}</template>

          </v-data-table>
        </v-card>
      </v-row>
    </v-container>

    <filter-nav-drawer
      ref="editFilter"
      :tabs="tabs"
      :filtersData="filters"
      @apply-filter="applyFilters"
    ></filter-nav-drawer>
  </div>
</template>

<script setup>
import { onBeforeMount, ref, computed, watch } from "vue";
import { useRouter } from "vue-router";
import { useClosingStore } from "@/stores/closing";

import { useLocationStore } from "@/stores/location";
import { tableHeaders } from "@/helpers/tableHeaders";
import { filterData } from "@/helpers/searchFilter";
import ListActionsBar from "@/components/utils/ListActionsBar.vue";
import FilterNavDrawer from "@/components/filters/NavDrawer.vue";
import AutoComplete from "@/components/filters/Autocomplete.vue";
import {
  ConvertIOStoDate,
  ConvertIOStoTime,
  dateFormat,
  timeFormat,
} from "@/helpers/date";

const closingStore = useClosingStore();
const locationStore = useLocationStore();
const router = useRouter();

const search = ref(null);
const loading = ref(false);
const editFilter = ref(null);

const headers = ref(tableHeaders["closingListHeaders"]);
const sortBy = ref([{ key: "itemName", order: "asc" }]);

const items = computed(() => closingStore.getClosingData);
const locations = computed(() => locationStore.getLocations);

const filteredItems = computed(() => {
  const query = search.value?.toLowerCase() || "";
  let result = query ? filterData(items.value, query) : items.value;
  return result;
});

const categories = computed(() => [
  ...new Map(items.value.map(i => [i.category?.id, i.category])).values(),
].filter(Boolean));

const subCategories = computed(() => [
  ...new Map(items.value.map(i => [i.subCategory?.id, i.subCategory])).values(),
].filter(Boolean));

const tabs = [{ value: 1, label: "filters" }];

const filters = computed(() => [
  {
    component: AutoComplete,
    title: "WorkArea",
    key: "locationId",
    items: locations.value,
    default: true,
  },
]);

const dataFilters = ref({});

const loadData = ref(true);

const applyFilters = (filters) => {  
  dataFilters.value = filters;
  if (!loadData.value) {
    refresh();
  }
};

const toggleFilter = () => {
  editFilter.value.toggle();
};

const handleSearch = v => {
  search.value = v;
};

const add = () => {
  router.push({
    name: "create-closing",
  });
};

const refresh = async () => {
  try {
    loading.value = true;
    search.value = null;
    await closingStore.fetchClosingData(dataFilters.value);
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
};

onBeforeMount(async () => {
  await locationStore.fetchLocations();
  await refresh();
  loadData.value = false;
});
</script>

<style scoped>
</style>
