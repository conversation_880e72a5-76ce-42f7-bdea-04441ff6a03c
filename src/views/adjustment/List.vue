<template>
  <div>
    <!-- List Actions Bar -->
    <list-actions-bar
      search-label="Search Adjustments"
      add-label="Adjustment"
      :hide-filter="true"
      :hide-import-export="true"
      @refresh="refresh"
      @add="add"
    />

    <!-- Main content -->
    <v-container fluid>
      <v-row no-gutters>
        <v-card border rounded="lg" width="100%">
          <v-data-table
            class="table-bordered"
            :headers="adjustmentListHeaders"
            :items="filteredItems"
            :loading="loading"
            :hide-default-footer="filteredItems.length < 11"
            no-data-text="No Adjustments found"
          >
            <!-- Loading Skeleton -->
            <template #loading>
              <v-skeleton-loader type="table-row@10"></v-skeleton-loader>
            </template>
            <!-- Loader -->
            <template #loader>
              <v-progress-linear
                :height="2"
                indeterminate
                color="primary"
              ></v-progress-linear>
            </template>

            <!-- content -->
          </v-data-table>
        </v-card>
      </v-row>
    </v-container>
  </div>
</template>

<script setup>
import { onBeforeMount, ref, computed } from "vue";
import { useRouter } from "vue-router";
import { useAdjustmentStore } from "@/stores/adjustment";
import { adjustmentListHeaders } from "@/helpers/tableHeaders";

import ListActionsBar from "@/components/utils/ListActionsBar.vue";

const adjustmentStore = useAdjustmentStore();
const router = useRouter();

const filterState = ref(null);
const loading = ref(false);

const items = computed(() => adjustmentStore.getAdjustments || []);

const filteredItems = computed(() => {
  const query = filterState.value.search?.toLowerCase() || "";
  let result = query ? filterData(items.value, query) : items.value;
  return result;
});

const add = () => {
  router.push({ name: "create-adjustment" });
};

const fetch = async () => {
  await adjustmentStore.fetchAdjustments();
};

const resetFilter = () => {
  filterState.value = {
    search: null,
  };
};

const refresh = async () => {
  try {
    loading.value = true;
    resetFilter();
    await fetch();
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
};

onBeforeMount(() => {
  refresh();
});
</script>

<style scoped></style>
