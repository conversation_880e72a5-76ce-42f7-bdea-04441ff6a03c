<template>
  <div>
    <!-- Report Header -->
    <ReportHeader
      v-model="payload"
      enable-date
      enable-vendors
      :enable-items="!disableItemFilter"
      :loading="loading"
      @search="onSearch"
      :enable-export="['xlsx']"
    />

    <!-- REPORT -->
    <v-container fluid v-if="rt === ResultType.JSON && !error">
      <v-row no-gutters>
        <v-card border rounded="lg" width="100%">
          <v-data-table
            class="table-bordered"
            :headers="response.headers"
            :items="response.data"
            :loading="loading"
          >
            <!-- Loading Skeleton -->
            <template #loading>
              <v-skeleton-loader type="table-row@10"></v-skeleton-loader>
            </template>

            <!-- Loader -->
            <template #loader>
              <v-progress-linear :height="2" indeterminate color="primary"></v-progress-linear>
            </template>
          </v-data-table>
        </v-card>
      </v-row>
    </v-container>
    <!-- Message/Loader -->
    <ResultMessage v-else :loading="loading" :rt="rt" :error="error" />
  </div>
</template>

<script setup>
import { ref } from "vue";

import httpClient from "@/plugin/Axios";
import { ResultType } from "@/plugin/Axios";
import { NewReportPayload } from "@/helpers/useReportPayload";

import ReportHeader from "@/components/utils/ReportHeader.vue";
import ResultMessage from "./ResultMessage.vue";

const props = defineProps({
  id: {
    // report reference id
    type: String,
    required: true
  },
  url: {
    // fetch url
    type: String,
    required: true
  },
  disableItemFilter: {
    type: Boolean,
    default: false
  }
});

const loading = ref(false);
const payload = ref(NewReportPayload(props.id));
const rt = ref(null);
const error = ref(null);
const response = ref({
  data: [],
  totalRow: {},
  headers: []
});

const onSearch = async ({ reqPayload, resultType = ResultType.JSON }) => {
  error.value = null;
  loading.value = true;
  rt.value = resultType; // record last result type
  response.value = {};

  try {
    const config = { resultType };
    if (resultType !== ResultType.JSON) {
      config.responseType = "blob";
    }
    const res = await httpClient.post(
      `${props.url}?resultType=${resultType}`,
      reqPayload,
      config
    );
    response.value = res.data;
  } catch ({ response }) {
    error.value = response.data?.error || "Something went wrong.";
  } finally {
    loading.value = false;
  }
};
</script>
