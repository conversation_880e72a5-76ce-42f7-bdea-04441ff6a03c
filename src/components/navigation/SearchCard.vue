<template>
  <v-card
    border
    rounded="lg"
    color="surface-variant"
    class="w-100"
    title="Search"
    append-icon="mdi-magnify"
    @keydown.stop
  >
    <template #subtitle>
      <div class="text-lowercase">Search PR, PO, GRN, transfer, dispatch, contract etc.</div>
    </template>

    <v-card-text class="py-5">
      <v-text-field
        v-model="searchQuery"
        placeholder="Type PO/PR/GRN/TF/DIS/CT & number"
        variant="outlined"
        color="primary"
        persistent-hint
        hint="Search like: PR (PR001), PO (PO1234), GRN (GRN0001), Transfer (T1002), Dispatch (DIS1234), Contract (CT5678)"
        clearable
        autofocus
        :loading="loading"
        :disabled="loading"
        @keyup.enter="search"
      />
    </v-card-text>
  </v-card>
</template>

<script setup>
import { ref } from "vue";
import { useRouter } from "vue-router";
import httpClient from "@/plugin/Axios";
import { useSnackbarStore } from "@/stores/snackBar";
import COUNTER_TYPES from "@/constants/counterDefs";
import { checkUserPrivilege } from "@/router/middleware";
import { PRIV_CODES } from "@/constants/privilegeCodes";

const snackbarStore = useSnackbarStore();
const router = useRouter();

const emit = defineEmits(["close"]);
const searchQuery = ref("");
const loading = ref(false);

/**
 * Map counter type constants to route and privilege
 */
const counterMap = {
  [COUNTER_TYPES.PURCHASE_REQUEST]: {
    route: "view-purchase-request",
    privilege: PRIV_CODES.PUR_PR
  },
  [COUNTER_TYPES.PURCHASE_ORDER]: {
    route: "view-purchase-order",
    privilege: PRIV_CODES.PUR_PO
  },
  [COUNTER_TYPES.TRANSFER]: {
    route: "view-transfer",
    privilege: PRIV_CODES.TRANSFER
  },
  [COUNTER_TYPES.GRN]: { route: "view-grn", privilege: PRIV_CODES.GRN },
  // [COUNTER_TYPES.VENDOR]: { route: "vendor-details", privilege: PRIV_CODES.VENDOR },
  // [COUNTER_TYPES.INVENTORY_ITEM]: { route: "inventory-item-details", privilege: PRIV_CODES.INVENTORY_ITEM },
  // [COUNTER_TYPES.RECIPE]: { route: "recipe-details", privilege: PRIV_CODES.RECIPE },
  // [COUNTER_TYPES.PACKAGE]: { route: "package-details", privilege: PRIV_CODES.PACKAGE },
  [COUNTER_TYPES.DISPATCH]: {
    route: "view-dispatch",
    privilege: PRIV_CODES.DISPATCH
  },
  // [COUNTER_TYPES.CLOSING]: { route: "closing-details", privilege: PRIV_CODES.CLOSING },
  [COUNTER_TYPES.CONTRACT]: {
    route: "edit-contract",
    privilege: PRIV_CODES.CONTRACT
  }
};

const search = async () => {
  if (!searchQuery.value) {
    snackbarStore.showSnackbar("error", "Please enter a search term");
    return;
  }

  // Validate number format: <PREFIX><4 digits>
  const match = searchQuery.value.match(/^([A-Za-z]+)(\d{4})$/);
  if (!match) {
    const errMsg = `Invalid number format: ${searchQuery.value}. Expected format: <PREFIX><4 digits> e.g., PO1234`;
    snackbarStore.showSnackbar("error", errMsg);
    return;
  }

  const prefix = match[1].toUpperCase();
  // Find counter constant by value
  const counterKey = Object.keys(COUNTER_TYPES).find(
    key => COUNTER_TYPES[key] === prefix
  );

  if (!counterKey) {
    snackbarStore.showSnackbar(
      "error",
      `Invalid search term: ${searchQuery.value}`
    );
    return;
  }

  const counterInfo = counterMap[COUNTER_TYPES[counterKey]];

  // Check privilege
  if (!checkUserPrivilege(counterInfo.privilege)) {
    snackbarStore.showSnackbar(
      "error",
      "You do not have access to this module"
    );
    return;
  }

  loading.value = true;
  try {
    const response = await httpClient.get(
      `/action-center/search?number=${searchQuery.value}`
    );
    const { result } = response.data;

    if (!result) {
      snackbarStore.showSnackbar("error", "No results found");
      return;
    }

    router.push(
      counterInfo.route === "view-dispatch"
        ? { name: counterInfo.route, params: { id: result.id }, query: { dispatchId: searchQuery.value } }
        : { name: counterInfo.route, params: { id: result.id } }
    );

    // router.push({ name: counterInfo.route, params: { id: result.id } });
    emit("close");
  } catch (error) {
    snackbarStore.showSnackbar(
      "error",
      error.response?.data?.message || "Search failed"
    );
  } finally {
    loading.value = false;
  }
};
</script>

