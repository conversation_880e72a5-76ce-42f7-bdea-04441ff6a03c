<template>
  <div>
    <v-navigation-drawer
      v-model="openNav"
      fixed
      location="right"
      width="380"
      class="filter-elem"
      order="-1"
      persistent
      temporary
    >
      <v-card flat>
        <v-container fluid class="pa-0">
          <v-card-text class="pa-0">
            <div
              class="d-flex justify-space-between align-center pr-3"
            >
              <v-tabs v-model="tab" slider-color="primary">
                <v-tab
                  v-for="tab in tabs"
                  :key="tab.value"
                  :value="tab.value"
                  >{{ tab.label }}</v-tab
                >
              </v-tabs>
              <v-btn
                variant="text"
                icon="mdi-close"
                color="error"
                @click="openNav = false"
              />
            </div>

            <v-divider></v-divider>
            <v-tabs-window v-model="tab">
              <v-tabs-window-item :value="1">
                <component
                  v-for="filter in filtersData"
                  :key="filter.key"
                  :is="filter.component"
                  :filter="filter"
                ></component>
              </v-tabs-window-item>
            </v-tabs-window>
          </v-card-text>
        </v-container>
      </v-card>
      <template v-slot:append>
        <div class="pa-2">
          <v-btn block color="primary" @click="applyFilters">
            Apply Filter</v-btn
          >
        </div>
      </template>
    </v-navigation-drawer>
  </div>
</template>
<script setup>
import { ref, provide, onMounted } from "vue";

const props = defineProps({
  tabs: {
    type: Array,
    default: () => [],
  },
  filtersData: {
    type: Array,
    default: () => [],
  },
});

const tab = ref(1);
const openNav = ref(false);

const emit = defineEmits(["applyFilter"]);

const toggle = () => {
  openNav.value = !openNav.value;
};

// used to expose function to be called by external actions
defineExpose({
  toggle,
});

const filters = ref({});
provide("filters", filters);
const applyFilters = () => {
  closeNav();
  emit("applyFilter", filters.value);
};

const closeNav = () => {
  openNav.value = false;
};

onMounted(() => applyFilters());
</script>
