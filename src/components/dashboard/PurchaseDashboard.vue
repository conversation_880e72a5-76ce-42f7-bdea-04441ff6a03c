<template>
  <v-container fluid>
    <v-row>
      <v-col cols="12">
        <widget-metrics :loading="loading" :items="metrics" />
      </v-col>
      <v-col cols="12">
        <widget :widget="dailyTrendWidgetData" />
      </v-col>
      <v-col cols="12">
        <widgets :widgets="widgetData" />
      </v-col>
      <v-col></v-col>
    </v-row>
  </v-container>
</template>

<script setup>
import { computed, onMounted } from "vue";

// definitions
import { metricDefs, widgetDefs } from "./helpers/purchaseDashboardHelper.js";

// components
import WidgetMetrics from "./widgets/WidgetMetrics.vue";
import Widgets from "./widgets/Widgets.vue";
import Widget from "./widgets/Widget.vue";

const props = defineProps({
  dashboardData: {
    type: Object,
    required: true,
  },
  loading: {
    type: Boolean,
    default: false,
  },
});

const metrics = computed(() =>
  metricDefs.map((metric) => ({
    ...metric,
    value: props.dashboardData[metric.key] ?? metric.value,
  }))
);

const widgetData = computed(() => {
  // Map widgetDefs to array, attach dataset
  return [
    widgetDefs.topVendors,
    widgetDefs.locationPurchaseAnalysis,
    widgetDefs.categoryPurchaseDistribution,
    widgetDefs.highValueItems,
  ].map((widget) => ({
    ...widget,
    dataset: props.dashboardData[widget.id] || [],
  }));
});

const dailyTrendWidgetData = computed(() => ({
  ...widgetDefs.dailyPurchaseTrends,
  dataset: props.dashboardData[widgetDefs.dailyPurchaseTrends.id] || [],
}));
</script>

<style scoped></style>
