<template>
  <v-container fluid class="px-0 py-2">
    <v-row no-gutters>
      <v-card border rounded="lg" class="my-2" width="100%">
        <v-data-table
          class="table-bordered"
          :items="prList"
          :headers="headers"
          items-per-page="-1"
          hide-default-footer
        >
          <template #item.index="{ index }">{{ index + 1 }}</template>
          <template #item.actions="{ index }">
            <v-icon
              color="error"
              @click="$emit('removeItem', index)"
              @keydown.enter="$emit('removeItem', index)"
              >mdi-close</v-icon
            >
          </template>

          <template #item.itemName="{ item }">
            <formatted-item-name :item="item"></formatted-item-name>
          </template>

          <template #item.quantity="{ item, index }">
            <v-text-field
              v-model.number="item.quantity"
              type="number"
              min="1"
              variant="outlined"
              density="compact"
              color="primary"
              hide-details="auto"
              @keydown.up.prevent
              @keydown.down.prevent
              @blur="() => blurField(item, 'quantity')"
              @update:model-value="$emit('edit', item, index)"
            ></v-text-field>
          </template>

          <template #item.purchaseUOM="{ item }">{{
            item.pkgUOM || item.purchaseUOM
          }}</template>

          <template #item.unitCost="{ item, index }">
            <v-text-field
              v-model.number="item.unitCost"
              type="number"
              variant="outlined"
              density="compact"
              color="primary"
              hide-details="auto"
              :readonly="!!item.contractId"
              persistent-hint
              :hint="item.contractNumber"
              @keydown.up.prevent
              @keydown.down.prevent
              @blur="() => blurField(item, 'unitCost')"
              @update:model-value="$emit('edit', item, index)"
              min-width="100px"
            >
              <!-- indicate contract -->
              <template #prepend-inner v-if="item.contractType">
                <v-icon size="small" icon="mdi-file-sign" color="purple" />
              </template>
            </v-text-field>
          </template>

          <template #item.totalDiscount="{ item, index }">
            <v-text-field
              v-model.number="item.totalDiscount"
              type="number"
              variant="outlined"
              density="compact"
              color="primary"
              hide-details="auto"
              @keydown.up.prevent
              @keydown.down.prevent
              @blur="() => blurField(item, 'totalDiscount')"
              @update:model-value="$emit('edit', item, index)"
            ></v-text-field>
          </template>

          <template #item.totalCess="{ item, index }">
            <v-text-field
              v-model.number="item.totalCess"
              type="number"
              variant="outlined"
              density="compact"
              color="primary"
              hide-details="auto"
              @keydown.up.prevent
              @keydown.down.prevent
              @blur="() => blurField(item, 'totalCess')"
              @update:model-value="$emit('edit', item, index)"
            ></v-text-field>
          </template>

          <template #item.taxRate="{ item, index }">
            <v-text-field
              v-model.number="item.taxRate"
              density="compact"
              color="primary"
              hide-details="auto"
              suffix="%"
              type="number"
              variant="outlined"
              @keydown.up.prevent
              @keydown.down.prevent
              @blur="() => blurField(item, 'taxRate')"
              @update:model-value="$emit('edit', item, index)"
            ></v-text-field>
          </template>
          <template #item.totalExclusiveTax="{ item }">{{
            truncateNumber(item.netAmount)
          }}</template>

          <template #item.taxAmount="{ item }">{{
            truncateNumber(item.totalTaxAmount)
          }}</template>

          <template #item.totalInclusiveTax="{ item }">{{
            truncateNumber(item.totalAmount)
          }}</template>

          <!-- cart summary -->
          <template #no-data>
            <v-btn
              :disabled="!formValid"
              prepend-icon="mdi-plus"
              text="Add Item"
              variant="text"
              border
              rounded="xl"
              @click="$emit('addItem')"
            ></v-btn>
          </template>
        </v-data-table>
      </v-card>
    </v-row>
  </v-container>
</template>

<script setup>
import { truncateNumber } from "@/helpers/money";
import FormattedItemName from "@/components/purchase/viewTable/FormattedItemName.vue";

const props = defineProps({
  prList: {
    type: Array,
    default: () => [],
  },
  headers: {
    type: Array,
    default: () => [],
  },
  formValid: {
    type: Boolean,
    default: false,
  },
  showVendor: {
    type: Boolean,
    default: true,
  },
});

const blurField = (item, field) => {
  // Ensure item and field exist
  if (!item || typeof field !== "string") return;

  // Special rule: quantity must always be at least 1
  if (field === "quantity") {
    if (item.quantity < 1 || !item.quantity) {
      item.quantity = 1;
    }
    return;
  }

  // Generic rule: any numeric field should not go below 0 or be falsy (NaN, null, etc.)
  if (item[field] < 0 || !item[field]) {
    item[field] = 0;
  }

  // Logical constraint: discount cannot exceed unit cost
  if (item.discount > item.unitCost * item.quantity) {
    item.discount = item.unitCost * item.quantity;
  }
};

const emit = defineEmits(["removeItem", "edit", "addItem"]);
</script>
