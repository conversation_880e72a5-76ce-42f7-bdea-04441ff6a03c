<template>
  <v-container fluid class="px-0">
    <v-card border rounded="lg" width="100%">
      <v-data-table
        class="table-bordered"
        :items="items"
        :headers="headers"
        items-per-page="-1"
        hide-default-footer
      >
        <template #item.index="{ index }">{{ index + 1 }}</template>

        <template #item.itemName="{ item }">
          <formatted-item-name :item="item"></formatted-item-name>
        </template>

        <template #item.orderedQty="{ item }">
          {{ item.quantity }}
        </template>

        <template #item.purchaseUOM="{ item }">
          {{ item.pkgUOM || item.purchaseUOM }}
        </template>

        <template #item.unitCost="{ item }">
          {{ truncateNumber(item.unitCost) }}
        </template>

        <template #item.discount="{ item }">
          {{ truncateNumber(item.discount) }}
        </template>

        <template #item.taxRate="{ item }"> {{ item.taxRate }}% </template>

        <template #item.totalExclusiveTax="{ item }">
          {{ truncateNumber(item.quantity * item.unitCost - item.discount) }}
        </template>

        <template #item.taxAmount="{ item }">
          {{ truncateNumber(item.taxAmount) }}
        </template>

        <template #item.totalInclusiveTax="{ item }">{{
          truncateNumber(item.totalPrice)
        }}</template>

        <template #item.totalPrice="{ item }">
          {{ truncateNumber(item.totalPrice) }}
        </template>

        <!-- 👇 Aligned Footer Totals -->
        <template #tfoot>
          <tr class="sticky-bottom-row">
            <!-- index -->
            <td></td>
            <!-- Item Name -->
            <td class="font-weight-bold">Total</td>
            <!-- vendor -->
            <td v-if="showVendor"></td>
            <!-- stock -->
            <td></td>
            <!-- order quantity -->
            <td></td>
            <!-- UOM -->
            <td></td>
            <!-- unit cost -->
            <td></td>
            <!-- Discount amt -->
            <td></td>
            <!-- Cess -->
            <td></td>
            <!-- Tax Rate -->
            <td></td>
            <!-- Exclusive Tax Total -->
            <td class="text-end font-weight-bold pr-4">
              {{ cart.netAmount }}
            </td>
            <td class="text-end font-weight-bold pr-4">
              {{ cart.totalTaxAmount }}
            </td>
            <!-- Inclusive Tax Total -->
            <td class="text-end font-weight-bold pr-4">
              {{ cart.totalAmount }}
            </td>
          </tr>
        </template>
      </v-data-table>
    </v-card>
  </v-container>
</template>

<script setup>
import { computed } from "vue";
import { truncateNumber } from "@/helpers/money";
import { purchaseRequestItemHeaders } from "@/helpers/tableHeaders";
import FormattedItemName from "@/components/purchase/viewTable/FormattedItemName.vue";

const props = defineProps({
  items: {
    type: Array,
    default: () => [],
  },
  showVendor: {
    type: Boolean,
    default: true,
  },
  cart: {
    type: Object,
    default: () => {},
  },
});

const headers = computed(() => {
  let headers = purchaseRequestItemHeaders.filter((h) => h.key !== "actions");
  if (!props.showVendor)
    headers = headers.filter((h) => h.key !== "vendor.name");
  return headers;
});
</script>
