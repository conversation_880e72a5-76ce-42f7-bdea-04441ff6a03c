<template>
  <div class="py-1">
    <span>{{ item.itemName }}</span>
    <div v-if="item.pkg && item.pkg.name" class="text-caption">
      <span class="text-grey-darken-1">Package:</span>
      {{ item.pkg.name }}
    </div>
    <div v-if="item.itemCode" class="text-caption">
      <span class="text-grey-darken-1">Item Code:</span>
      {{ item.itemCode }}
    </div>
    <div v-if="item.hsnCode" class="text-caption">
      <span class="text-grey-darken-1">HSN Code:</span>
      {{ item.hsnCode }}
    </div>
    <div v-if="item.packageName" class="text-caption">
      <span class="text-grey-darken-1">Package:</span>
      {{ item.packageName }}
    </div>
    <div v-if="item.remarks" class="text-caption">
      <span class="text-grey-darken-1">Remarks:</span>
      {{ item.remarks }}
    </div>
    <div
      v-if="showFoc"
      @click.native="toggleFoc"
      :class="{ 'cursor-pointer': editable }"
      class="text-caption"
    >
      <v-icon size="small" :color="item.foc ? 'success' : ''"
        >mdi-tag-heart</v-icon
      >
      FOC: {{ item.foc ? "Yes" : "No" }}
    </div>
  </div>
</template>
<script setup>
const props = defineProps({
  item: {
    type: Object,
    required: true,
  },
  showFoc: {
    type: Boolean,
    default: false,
  },
  editable: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(["updateFoc"]);

const toggleFoc = () => {
  if (!props.editable) return;
  props.item.foc = !props.item.foc;
  emit("updateFoc");
};
</script>
