<template>
  <v-container fluid class="pa-0">
    <v-row no-gutters>
      <v-card border rounded="lg" class="my-2" width="100%">
        <v-data-table
          class="table-bordered"
          :items="items"
          :headers="grnListHeaders"
          items-per-page="-1"
        >
          <template #item.index="{ index }">{{ index + 1 }}</template>
          <template #item.itemName="{ item }">
            <formatted-item-name :item="item" show-foc></formatted-item-name>
          </template>

          <template #item.unitCost="{ item }">
            <span>{{ truncateNumber(item.unitCost) }}</span>
          </template>

          <template #item.discount="{ item }">
            <span>{{ truncateNumber(item.discount) }}</span>
          </template>

          <template #item.taxRate="{ item }">
            <span>{{ item.taxRate }}%</span>
          </template>

          <template #item.totalExclusiveTax="{ item }">
            {{
              truncateNumber(item.receivedQty * item.unitCost - item.discount)
            }}
          </template>

          <template #item.taxAmount="{ item }">{{
            truncateNumber(item.taxAmount)
          }}</template>

          <template #item.totalInclusiveTax="{ item }">{{
            truncateNumber(item.totalValue)
          }}</template>

          <template #bottom>
            <cart-summary
              :charges="record.charges"
              :data="{
                total: record.totalAmount,
                subtotal: record.netAmount,
                discount: record.totalDiscount,
                focAmount: record.totalFocAmount,
                transportCharge: 0,
                otherCharges: 0,
                totalTaxAmount: record.totalTaxAmount,
              }"
            />
          </template>
        </v-data-table>
      </v-card>
    </v-row>
  </v-container>
</template>

<script setup>
import { truncateNumber } from "@/helpers/money";
import { grnListHeaders } from "@/helpers/tableHeaders";
import FormattedItemName from "@/components/purchase/viewTable/FormattedItemName.vue";
import CartSummary from "../CartSummary.vue";

const props = defineProps({
  items: {
    type: Array,
    default: () => [],
  },
  record: {
    type: Object,
    default: () => {},
  },
});
</script>
