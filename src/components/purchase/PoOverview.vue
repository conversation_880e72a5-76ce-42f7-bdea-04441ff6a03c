<template>
  <v-card border rounded="lg" v-if="show">
    <v-expansion-panels variant="accordion" multiple flat focusable border>
      <!-- PO Details -->
      <v-expansion-panel v-if="data.id">
        <v-expansion-panel-title>
          #{{ data.poNumber }}
          <template #actions>
            <v-btn
              :color="getStatusColor(data.status)"
              variant="tonal"
              :text="data.status"
              size="small"
            />
            <v-icon class="ml-2" />
          </template>
        </v-expansion-panel-title>
        <v-expansion-panel-text>
          <LabelValueView :details="poDetails" />
        </v-expansion-panel-text>
        <v-divider />
      </v-expansion-panel>

      <!-- Vendor Details -->
      <v-expansion-panel v-if="data.vendor">
        <v-expansion-panel-title>Vendor (#{{ data.vendor.vendorId }})</v-expansion-panel-title>
        <v-expansion-panel-text>
          <LabelValueView :details="vendorDetails" />
        </v-expansion-panel-text>
        <v-divider />
      </v-expansion-panel>

      <!-- PO Terms -->
      <v-expansion-panel v-if="data.vendor">
        <v-expansion-panel-title>PO Terms</v-expansion-panel-title>
        <v-expansion-panel-text>
          <EditableTextView v-model="data.vendor.poTerms" title="PO Terms" :edit="editMode" />
        </v-expansion-panel-text>
        <v-divider />
      </v-expansion-panel>

      <!-- Payment Terms -->
      <v-expansion-panel v-if="data.vendor">
        <v-expansion-panel-title>Payment Terms</v-expansion-panel-title>
        <v-expansion-panel-text>
          <EditableTextView
            v-model="data.vendor.paymentTerms"
            title="Payment Terms"
            :edit="editMode"
          />
        </v-expansion-panel-text>
        <v-divider />
      </v-expansion-panel>

      <!-- Remarks -->
      <v-expansion-panel>
        <v-expansion-panel-title>Remarks</v-expansion-panel-title>
        <v-expansion-panel-text>
          <EditableTextView v-model="data.remarks" title="Remarks" :edit="editMode" />
        </v-expansion-panel-text>
      </v-expansion-panel>
    </v-expansion-panels>
  </v-card>
</template>

<script setup>
import { computed } from "vue";
import { getStatusColor } from "@/helpers/status";

import LabelValueView from "@/components/utils/LabelValueView.vue";
import EditableTextView from "@/components/utils/EditableTextView.vue";

const props = defineProps({
  data: {
    type: Object,
    required: true,
    default: () => ({})
  },
  editMode: {
    type: Boolean,
    default: false
  }
});

const poDetails = [
  { label: "Location", value: props.data.location?.name || "-" },
  { label: "Expected Delivery Date", value: props.data.deliveryDate || "-" },
  { label: "Created By", value: props.data.requestedBy?.name || "-" }
];

const vendorDetails = computed(() => {
  const vendor = props.data.vendor || {};

  const detailsList = [
    { label: "Vendor Name", value: vendor.name || "-" },
    { label: "Contact Name", value: vendor.contactName || "-" },
    { label: "Contact", value: vendor.contactNo || "-" },
    { label: "Email", value: vendor.contactEmailId || "-" }
  ];

  // Conditionally add GSTIN, PAN, CIN, TIN if present
  if (vendor.gstNo) detailsList.push({ label: "GSTIN", value: vendor.gstNo });
  if (vendor.panNo) detailsList.push({ label: "PAN", value: vendor.panNo });
  if (vendor.cinNo) detailsList.push({ label: "CIN", value: vendor.cinNo });
  if (vendor.tinNo) detailsList.push({ label: "TIN", value: vendor.tinNo });

  return detailsList;
});

const show = computed(() => props.data.id || props.data.vendor);
</script>

<style scoped></style>
