<template>
  <div>
    <v-fab
      v-if="!openNav"
      :disabled="!formValid"
      color="primary"
      app
      extended
      prepend-icon="mdi-plus"
      text="Add Item"
      location="bottom right"
      @click="openNav = true"
    ></v-fab>

    <v-navigation-drawer
      v-model="openNav"
      fixed
      location="right"
      width="320"
      class="filter-elem"
      persistent
    >
      <!-- Header: Tabs + Close Button -->
      <template v-slot:prepend>
        <v-toolbar density="compact" title="Add Item">
          <v-spacer></v-spacer>
          <v-btn
            variant="text"
            icon="mdi-close"
            color="error"
            @click="openNav = false"
          />
        </v-toolbar>
      </template>

      <v-card flat tile>
        <!-- Filters -->
        <InventoryFiltersPanel
          v-model="filter"
          :selected-vendor-id="selectedVendorId"
        />

        <v-card-text>
          <v-form ref="form">
            <v-card-text class="px-0">
              <v-row>
                <v-col cols="12">
                  <inventory-item-field
                    ref="itemField"
                    label="Inventory Item"
                    v-model="selectedItem"
                    return-object
                    mandatory
                    @update:model-value="onSelectInventoryItem"
                    persistent-hint
                    :hint="`HSN Code: ${cartItem.hsnCode || '-'}`"
                    :categories="filter.categories"
                    :sub-categories="filter.subCategories"
                    :vendors="filter.vendors"
                    tabindex="0"
                  />
                </v-col>
                <v-col cols="12" v-if="!selectedVendorId && selectedItem">
                  <vendor-field
                    :only="selectedItem.vendors"
                    v-model="cartItem.vendor"
                    return-object
                    @update:model-value="onChangeVendor"
                  />
                </v-col>

                <v-col cols="12" v-if="packageList.length">
                  <v-autocomplete
                    v-model="cartItem.pkg"
                    :items="packageList"
                    item-title="name"
                    item-value="id"
                    label="Package"
                    :rules="[rules.require]"
                    variant="outlined"
                    density="compact"
                    color="primary"
                    clearable
                    hide-details="auto"
                    return-object
                    @update:model-value="handleInventoryPkg"
                  />
                </v-col>
                <v-col cols="12">
                  <v-text-field
                    v-model.number="cartItem.quantity"
                    label="Quantity"
                    type="number"
                    min="1"
                    :rules="[rules.positive]"
                    variant="outlined"
                    density="compact"
                    color="primary"
                    persistent-hint
                    :hint="`In Stock: ${cartItem.inStock || 0}`"
                    @keydown.up.prevent
                    @keydown.down.prevent
                    @blur="blurField(cartItem, 'quantity')"
                  >
                    <template v-slot:append-inner>{{
                      cartItem.pkgUOM || cartItem.purchaseUOM
                    }}</template>
                  </v-text-field>
                </v-col>
                <v-col cols="12">
                  <v-text-field
                    v-model.number="cartItem.unitCost"
                    label="Unit Cost"
                    type="number"
                    :rules="[rules.price, rules.positive]"
                    variant="outlined"
                    density="compact"
                    color="primary"
                    hide-details="auto"
                    :readonly="!!cartItem.contractId"
                    persistent-hint
                    :hint="cartItem.contractNumber"
                    @keydown.up.prevent
                    @keydown.down.prevent
                    @blur="blurField(cartItem, 'unitCost')"
                  >
                    <!-- indicate contract -->
                    <template #prepend-inner v-if="cartItem.contractId">
                      <v-icon
                        size="small"
                        icon="mdi-file-sign"
                        color="purple"
                      />
                    </template>

                    <template #append-inner>
                      <purchase-history
                        :item="cartItem"
                        :locationId="locationId"
                      />
                    </template>
                  </v-text-field>
                </v-col>
                <v-col cols="12">
                  <v-text-field
                    v-model.number="cartItem.totalDiscount"
                    label="Discount"
                    type="number"
                    :rules="[
                      rules.price,
                      rules.positive,
                      (v) =>
                        rules.maxValue(
                          v,
                          cartItem.unitCost * cartItem.quantity
                        ),
                    ]"
                    variant="outlined"
                    density="compact"
                    color="primary"
                    hide-details="auto"
                    @keydown.up.prevent
                    @keydown.down.prevent
                    @blur="blurField(cartItem, 'totalDiscount')"
                  ></v-text-field>
                </v-col>
                <v-col cols="12">
                  <v-text-field
                    v-model.number="cartItem.totalCess"
                    :rules="[rules.price, rules.positive]"
                    label="Cess"
                    type="number"
                    variant="outlined"
                    density="compact"
                    color="primary"
                    hide-details="auto"
                    @keydown.up.prevent
                    @keydown.down.prevent
                    @blur="blurField(cartItem, 'totalCess')"
                  ></v-text-field>
                </v-col>
                <v-col cols="12">
                  <tax-field
                    v-model="cartItem.taxes"
                    multiple
                    return-object
                    :hint="selectedTaxAmount"
                    persistent-hint
                  ></tax-field>
                </v-col>
                <v-col cols="12">
                  <v-text-field
                    v-model.number="cartItem.otherTax"
                    :rules="[rules.price, rules.positive]"
                    label="Other Tax"
                    type="number"
                    variant="outlined"
                    density="compact"
                    color="primary"
                    hide-details="auto"
                    suffix="%"
                    @keydown.up.prevent
                    @keydown.down.prevent
                    @blur="blurField(cartItem, 'otherTax')"
                  ></v-text-field>
                </v-col>
                <v-col cols="12">
                  <v-textarea
                    v-model.number="cartItem.remarks"
                    label="Remarks"
                    variant="outlined"
                    density="compact"
                    color="primary"
                    hide-details
                    rows="2"
                    @keydown.enter="handleSubmit"
                  ></v-textarea>
                </v-col>
              </v-row>
            </v-card-text>
          </v-form>
        </v-card-text>
      </v-card>

      <template #append>
        <v-card border tile>
          <!-- @todo: item summary -->
          <template #actions>
            <v-btn @click="handleSubmit" color="primary" variant="flat" block
              >Add</v-btn
            >
          </template>
        </v-card>
      </template>
    </v-navigation-drawer>
  </div>
</template>

<script setup>
import { nextTick, ref, computed, onMounted, watch } from "vue";
import rules from "@/helpers/rules";
import cartHelper from "@/helpers/cartHelper";

import { useInventoryItemStore } from "@/stores/inventoryItem";
import { useMasterStore } from "@/stores/masterStore";

// components
import InventoryFiltersPanel from "./InventoryFiltersPanel.vue";
import InventoryItemField from "@/components/fields/InventoryItemField.vue";
import VendorField from "@/components/fields/VendorField.vue";
import TaxField from "@/components/fields/TaxField.vue";
import PurchaseHistory from "./PurchaseHistory.vue";

const emit = defineEmits(["add"]);
const props = defineProps({
  formValid: {
    type: Boolean,
    default: false,
  },
  locationId: {
    type: String,
    default: "",
  },
  selectedVendorId: {
    type: String,
    default: null,
  },
});

const inventoryStore = useInventoryItemStore();
const masterStore = useMasterStore();

const form = ref();
const openNav = defineModel();
const filter = ref({
  categories: [],
  subCategories: [],
  vendors: [],
});

const cartItem = ref(cartHelper.NewCartItem());
const packageList = ref([]);
const selectedItem = ref([]);
const itemField = ref(null);

const taxes = computed(() => {
  return masterStore.getTaxes();
});

const selectedTaxAmount = computed(() => {
  const selectedTax = cartItem.value.taxes.reduce((acc, tax) => {
    return acc + tax.value;
  }, 0);

  const result = selectedTax + cartItem.value.otherTax;
  return `Total: ${String(result)}%`;
});

const getItemDetails = async ({ id, packages }, vendorId) => {
  const payload = {
    id: id,
    locationId: props.locationId,
    vendorId: vendorId,
  };
  if (packages.length > 0) {
    payload.pkgId = "default";
  }
  const item = await inventoryStore.fetchItemDetails(payload, true);

  // select default vendor
  cartItem.value.vendor =
    item.vendors.find((v) => v.id === vendorId) || item.vendors[0];

  cartItem.value.itemName = item.itemName;
  cartItem.value.itemCode = item.itemCode;
  cartItem.value.itemId = item.id;
  cartItem.value.categoryId = item.category?.id;
  cartItem.value.categoryName = item.category?.name;
  cartItem.value.subcategoryId = item.subCategory?.id;
  cartItem.value.subcategoryName = item.subCategory?.name;
  cartItem.value.hsnCode = item.hsnCode || "";
  cartItem.value.purchaseUOM = item.purchaseUnit.symbol;
  cartItem.value.contractPrice = item.contractPrice || 0;
  cartItem.value.unitCost = item.contractPrice
    ? item.contractPrice
    : item.unitCost;
  cartItem.value.contractType = item.contractType || null;
  cartItem.value.contractId = item.contractId || null;
  cartItem.value.contractNumber = item.contractNumber || null;
  cartItem.value.inStock = item.inStock;
  cartItem.value.inclTax = item.inclTax || false;

  // fetch tax object
  const ids = item.taxes.map((item) => item.id);
  cartItem.value.taxes =
    taxes.value.filter((tax) => ids.includes(tax.id)) || [];

  if (item.packages.length) {
    packageList.value = [
      { name: `${item.purchaseUnit.symbol} (default)`, id: "default" },
      ...item.packages,
    ];
    cartItem.value.pkg = {
      name: `${item.purchaseUnit.symbol} (default)`,
      id: "default",
    };
  } else {
    packageList.value = [];
    cartItem.value.pkg = null;
  }
};

const onSelectInventoryItem = async (selected) => {
  selectedItem.value = selected;
  cartItem.value.vendor = null;
  if (!selected) return;
  let vendorId = props.selectedVendorId;
  if (!vendorId) {
    if (selected.vendors.length != 1) {
      return;
    }
    vendorId = selected.vendors[0];
  }
  getItemDetails(selected, vendorId);
};

const onChangeVendor = (selected) => {
  if (!selected) return;
  getItemDetails(selectedItem.value, selected.id);
};

const handleInventoryPkg = async (v) => {
  if (!v) return;
  const item = await inventoryStore.fetchItemDetails(
    {
      id: cartItem.value.itemId,
      locationId: props.locationId,
      vendorId: props.selectedVendorId,
      pkgId: v.id,
    },
    true
  );
  cartItem.value.purchaseUOM = item.purchaseUnit.symbol;
  cartItem.value.pkgUOM =
    v.id === "default" ? item.purchaseUnit.symbol : v.name;
  cartItem.value.contractPrice = item.contractPrice || 0;
  cartItem.value.unitCost = item.contractPrice
    ? item.contractPrice
    : v.id === "default"
    ? item.unitCost
    : v.unitCost;
  cartItem.value.contractType = item.contractType || null;
  cartItem.value.inStock = item.inStock;
  cartItem.value.inclTax = item.inclTax || false;
};

const itemNameField = ref(null);
const handleSubmit = async () => {
  const { valid } = await form.value.validate();
  if (!valid) return;

  const payload = { ...cartItem.value };

  if (payload.pkg && payload.pkg.id === "default") {
    payload.pkg.quantity = payload.quantity;
  }
  if (!payload.pkg) {
    payload.pkg = {
      id: "default",
      name: "Default",
    };
  }
  emit("add", payload);
  packageList.value = [];

  // ✅ Reset form data
  cartItem.value = cartHelper.NewCartItem();
  selectedItem.value = null;
  form.value.resetValidation();
  // ✅ Focus back to the first field
  await nextTick();
  itemNameField.value?.focus();
  focusFirstField();
};

const blurField = (item, field) => {
  // Ensure item and field exist
  if (!item || typeof field !== "string") return;

  // Generic rule: any numeric field should not go below 0 or be falsy (NaN, null, etc.)
  if (item[field] < 0 || !item[field]) {
    item[field] = 0;
  }

  // Logical constraint: totalDiscount cannot exceed unit cost
  if (item.totalDiscount > item.unitCost) {
    item.totalDiscount = item.unitCost;
  }
};

let hasFocusedInitially = false;

const focusFirstField = () => {
  if (itemField.value?.$el) {
    const input = itemField.value.$el.querySelector("input, .v-input input");
    if (input) input.focus();
  }
};

onMounted(async () => {
  await nextTick();
  focusFirstField();
  hasFocusedInitially = true;

  window.addEventListener("keydown", (e) => {
    if (e.key === "Tab" && document.activeElement === document.body) {
      e.preventDefault();
      focusFirstField();
    }
  });
});

watch(openNav, async (val) => {
  if (val) {
    await nextTick();
    focusFirstField(); // same method you already have
  }
});
</script>
