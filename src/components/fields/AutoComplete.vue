<template>
  <v-autocomplete
    v-model="model"
    v-bind="$attrs"
    :label="title"
    :mandatory="mandatory"
    :multiple="multiple"
    :return-object="returnObject"
    :rules="rules"
    clearable
    hide-details="auto"
    variant="outlined"
    density="compact"
    color="primary"
  >
    <!-- selection view -->
    <template #selection="{ item, index }" v-if="multiple">
      <slot name="selection" :item="item" :index="index">
        <!-- {{ !intermediate ? `ALL ${$attrs["label"]}` : item.raw['name'] }} -->
        <span v-if="index === 0">{{ item.raw[$attrs["item-title"]] }}</span>
        <span v-if="index === 1" class="text-caption">(+ {{ model.length - 1 }} more)</span>
      </slot>
    </template>

    <!-- prepend -->
    <template #prepend-item v-if="multiple">
      <slot name="prepend-item">
        <v-list-item title="Select All" @click="toggleSelection">
          <template #prepend>
            <v-list-item-action>
              <v-checkbox-btn color="primary" :indeterminate="intermediate" :model-value="complete"></v-checkbox-btn>
            </v-list-item-action>
          </template>
        </v-list-item>
        <v-divider></v-divider>
      </slot>
    </template>

    <!-- forward slots -->
    <template v-for="(_, name) in $slots" v-slot:[name]="slotData">
      <slot :name="name" v-bind="slotData" />
    </template>
  </v-autocomplete>
</template>

<script setup>
import { computed, onMounted, useAttrs, nextTick } from "vue";
import rulesHelper from "@/helpers/rules";

// ----------------- Props -----------------
const props = defineProps({
  modelValue: {
    type: [String, Number, Array],
    default: null
  },
  label: {
    type: String,
    default: "Select"
  },
  multiple: {
    type: Boolean,
    default: false
  },
  mandatory: {
    type: Boolean,
    default: false
  },
  returnObject: {
    type: Boolean,
    default: false
  }
});

// ----------------- Model -----------------
const model = defineModel(); // replaces v-model

const attrs = useAttrs();
const id = attrs["item-value"] || attrs["item-title"];

let title = props.label;
if (props.mandatory) title = title + "*";

// Safely normalize modelValue to array
const selected = computed(() => {
  if (!model.value) return [];
  return Array.isArray(model.value) ? model.value : [model.value];
});

const items = computed(() => {
  return attrs.items.filter(
    i => i.type !== "subheader" && i.type !== "divider"
  );
});

// Semantic selection states
const complete = computed(
  () => props.multiple && selected.value.length === items.value.length
);
const intermediate = computed(
  () => props.multiple && selected.value.length > 0 && !complete.value
);

// Toggle all / clear all
function toggleSelection() {
  model.value = complete.value
    ? []
    : items.value.map(i => (props.returnObject ? i : i[id]));
}

// setDefault used to reset or set the default value
const setDefault = id => {
  nextTick(() => {
    model.value = null;
    if (!id) {
      return;
    }
    const val = items.value.find(item => item.id === id);
    if (!val) {
      return;
    }

    if (props.returnObject) {
      model.value = val;
    } else {
      model.value = val[id];
    }
  });
};

// Validation rules
const rules = props.mandatory
  ? props.multiple
    ? [rulesHelper.requireList]
    : [rulesHelper.require]
  : [];

onMounted(() => {
  // if mandatory only one item present select it
  if (props.mandatory && items.length === 1) {
    if (props.returnObject) {
      model.value = items[0];
    } else {
      model.value = items[0][id];
    }
  }
});

defineExpose({
  setDefault
});
</script>
