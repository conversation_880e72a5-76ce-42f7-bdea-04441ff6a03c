<template>
  <v-card class="border rounded-lg" width="100%">
    <v-data-table
      class="table-bordered"
      :headers="headers"
      :items="items"
      items-per-page="-1"
      hide-default-footer
      hide-no-data
      fixed-header
    >
      <template v-slot:item="{ item, index }">
        <tr class="text-center">
          <td class="py-2">{{ index + 1 }}</td>
          <td class="py-2 text-start">
            <div>
              <span>{{ item.itemName }}</span>
              <div v-if="item.itemCode" class="text-caption">
                <span class="text-grey-darken-1">Item Code:</span>
                {{ item.itemCode }}
              </div>
            </div>
          </td>
          <td class="py-2">
            <v-btn
              density="compact"
              :icon="
                item.adjustmentType == 'INCREASE'
                  ? 'mdi-arrow-up'
                  : 'mdi-arrow-down'
              "
              variant="tonal"
              color="primary"
              @click="() => handleAdjustmentType(item)"
            ></v-btn>
          </td>
          <td class="py-2">{{ item.quantity }} {{ item.countingUOM }}</td>
          <td class="py-2">{{ item.remarks }}</td>
          <td class="py-2">
            <div class="d-flex justify-center align-center">
              <v-icon color="error" @click="removeRow(index)">
                mdi-close
              </v-icon>
            </div>
          </td>
        </tr></template
      >

      <template v-slot:body.append>
        <tr class="text-center">
          <td class="py-2"></td>
          <td class="py-2">
            <v-autocomplete
              v-model="newRow.itemName"
              :items="itemList"
              item-title="itemName"
              item-value="id"
              density="compact"
              variant="outlined"
              single-line
              hide-details
              color="primary"
              return-object
              :rules="[rules.require]"
              @update:model-value="(val) => setValues(val, null)"
              clearable
              tabindex="-1"
            >
            </v-autocomplete>
          </td>
          <td class="py-2">
            <v-btn
              density="compact"
              :icon="
                newRow.adjustmentType == 'INCREASE'
                  ? 'mdi-arrow-up'
                  : 'mdi-arrow-down'
              "
              variant="tonal"
              color="primary"
              :disabled="!newRow.itemName"
              @click="() => handleAdjustmentType(newRow)"
            ></v-btn>
          </td>
          <td class="py-2">
            <v-text-field
              v-model.number="newRow.quantity"
              type="number"
              density="compact"
              variant="outlined"
              hide-details
              color="primary"
              :rules="[rules.require, rules.positive]"
              @keydown.up.prevent
              @keydown.down.prevent
              @blur="newRow.quantity = newRow.quantity || 0"
            >
              <template v-slot:append-inner>
                {{ newRow.countingUOM }}
              </template>
            </v-text-field>
          </td>
          <td class="py-2">
            <v-textarea
              v-model.trim="newRow.remarks"
              density="compact"
              variant="outlined"
              hide-details
              color="primary"
              rows="1"
              auto-grow
              :rules="[rules.require]"
              @keypress.enter="addRow(newRow)"
            />
          </td>
          <td class="py-2">
            <div class="d-flex justify-center align-center">
              <v-icon
                color="green"
                :disabled="!isRowValid(newRow)"
                @click="commitNewRow"
              >
                mdi-plus
              </v-icon>
            </div>
          </td>
        </tr>
      </template>
    </v-data-table>
  </v-card>
</template>

<script setup>
import { ref } from "vue";
import rules from "@/helpers/rules";
import { adjustmentItemHeaders } from "@/helpers/tableHeaders";
import { adjustmentItemRecord as DEFAULT_RECORD } from "@/helpers/defaultRecords";

const props = defineProps({
  itemList: {
    type: Array,
    default: () => [],
  },
});

const items = defineModel();
const headers = adjustmentItemHeaders;

const newRow = ref({ ...DEFAULT_RECORD });

const isRowValid = (row) => row.quantity > 0 && row.itemName && row.remarks;

const commitNewRow = () => {
  items.value.push({ ...newRow.value });
  newRow.value = { ...DEFAULT_RECORD };
};

const addRow = (row) => {
  if (!isRowValid(row)) return;
  commitNewRow(row);
};
const removeRow = (index) => {
  items.value.splice(index, 1);
};

const setValues = (val, row = null) => {
  const target = row || newRow.value;
  if (!val) return;

  target.itemName = val.itemName;
  target.itemId = val.id;
  target.itemCode = val.itemCode;
  target.countingUOM = val.countingUnit?.symbol;
  target.purchaseUOM = val.purchaseUnit?.symbol;
  target.unitCost = val.unitCost;

  target.adjustmentType =
    val.adjustmentType && val.adjustmentType == "INCREASE"
      ? "INCREASE"
      : "DECREASE";
};

const handleAdjustmentType = (row) => {
  const target = row;

  if (target.adjustmentType == "INCREASE") {
    target.adjustmentType = "DECREASE";
  } else {
    target.adjustmentType = "INCREASE";
  }
};
</script>
