/**
 * Procurement Report Definitions
 * -------------------------------
 * Centralized constants and configuration for all procurement reports.
 * Each report defines its columns, sort behavior, and optional aggregate keys.
 */

export const REPORTS = Object.freeze({
    // Procurement reports
    GRN: "grn-report",
    DETAILED_GRN: "grn-detailed-report",
    LOCATION_WISE_GRN: "grn-location-wise-report",
    VENDOR_WISE_GRN: "grn-vendor-wise-report",
    CATEGORY_WISE_GRN: "grn-category-wise-report",
    ITEM_WISE_GRN: "grn-item-wise-report",
    DAILY_GRN: "grn-daily-report",

    // Transfers reports
    TRANSFER_LIST: "transfer-list-report",
    DISPATCH_TRANSFER: "dispatch-transfer-report",
    DETAILED_TRANSFER: "detailed-transfer-report",
});

/**
 * @typedef {Object} ReportColumn
 * @property {string} key        - Key or path in data object
 * @property {string} header     - Column display name
 * @property {number} ordinal    - Display order (1..N)
 * @property {boolean} mandatory - Cannot be removed
 * @property {boolean} enable    - Enabled by default
 * @property {number} [width]    - Excel column width
 * @property {"start"|"center"|"end"} [align] - Optional alignment
 * @property {string} [destruct] - Optional field name to flatten nested arrays
 */

const AMOUNT_DETAIL_COLUMNS = [
    {
        title: "Gross Amount",
        key: "grossAmount",
        ordinal: 91,
        mandatory: false,
        enable: true,
        align: "end",
    },
    {
        title: "Discount",
        key: "totalDiscount",
        ordinal: 92,
        mandatory: false,
        enable: true,
        align: "end",
    },
    {
        title: "Cess",
        key: "totalCess",
        ordinal: 93,
        mandatory: false,
        enable: true,
        align: "end",
    },
    {
        title: "Net Amount",
        key: "netAmount",
        ordinal: 94,
        mandatory: false,
        enable: true,
        align: "end",
    },
    {
        title: "Foc",
        key: "totalFocAmount",
        ordinal: 95,
        mandatory: false,
        enable: true,
        align: "end",
    },
    // { title: "Charges", key: "charges", ordinal: 96, mandatory: false, enable: true, align: "end" },
    // { title: "Charge", key: "itemCharge", ordinal: 97, mandatory: false, enable: true,  align: "end" },
    // { title: "Taxes", key: "taxes", ordinal: 98, mandatory: false, enable: true, align: "end" },
    {
        title: "Total Tax",
        key: "totalTax",
        ordinal: 99,
        mandatory: false,
        enable: true,
        align: "end",
    },
    {
        title: "Total",
        subtitle: "(base - discount + charge + tax)",
        key: "totalValue",
        ordinal: 100,
        mandatory: true,
        enable: true,
        align: "end",
    },
];

/**
 * Example Column Sets
 */
export const REPORT_INFORMATION = Object.freeze({
    [REPORTS.GRN]: {
        id: REPORTS.GRN,
        name: "GRN REPORT",
        headers: [
            {
                title: "GRN No",
                key: "grnNumber",
                ordinal: 1,
                mandatory: true,
                enable: true,
            },
            // { title: "PO No", key: "poNumber", ordinal: 2, mandatory: true, enable: true,  },
            {
                title: "GRN DATE",
                key: "grnDate",
                ordinal: 2,
                mandatory: false,
                enable: true,
            },
            {
                title: "Invoice No",
                key: "invoiceNumber",
                ordinal: 3,
                mandatory: false,
                enable: true,
            },
            {
                title: "Invoice Date",
                key: "invoiceDate",
                ordinal: 4,
                mandatory: false,
                enable: true,
            },
            {
                title: "Vendor Name",
                key: "vendorName",
                ordinal: 5,
                mandatory: false,
                enable: true,
            },
            // { title: "Vendor Id", key: "vendorId", ordinal: 6, mandatory: false, enable: true,  },
            {
                title: "Location",
                key: "locationName",
                ordinal: 7,
                mandatory: false,
                enable: true,
            },
            {
                title: "Work/Storage Area",
                key: "inventoryLocationName",
                ordinal: 8,
                mandatory: false,
                enable: true,
            },
            {
                title: "Created At",
                key: "createdAt",
                ordinal: 9,
                mandatory: false,
                enable: true,
            },
            {
                title: "Created By",
                key: "createdByName",
                ordinal: 10,
                mandatory: false,
                enable: true,
            },
            ...AMOUNT_DETAIL_COLUMNS,
            // { title: "PO Terms", key: "poTerms", ordinal: 18, mandatory: false, enable: true,  },
            // { title: "Payment Terms", key: "paymentTerms", ordinal: 19, mandatory: false, enable: true,  },
        ],
        aggregateKey: null,
        options: {
            sortBy: "grnNumber",
            sortOrder: "desc",
        },
    },

    [REPORTS.DETAILED_GRN]: {
        id: REPORTS.DETAILED_GRN,
        name: "DETAILED GRN REPORT",
        headers: [
            {
                title: "GRN No",
                key: "grnNumber",
                ordinal: 1,
                mandatory: true,
                enable: true,
            },
            // { title: "PO No", key: "poNumber", ordinal: 2, mandatory: true, enable: true,  },
            {
                title: "GRN DATE",
                key: "grnDate",
                ordinal: 7,
                mandatory: false,
                enable: true,
            },
            {
                title: "Invoice No",
                key: "invoiceNumber",
                ordinal: 3,
                mandatory: false,
                enable: true,
            },
            {
                title: "Invoice Date",
                key: "invoiceDate",
                ordinal: 4,
                mandatory: false,
                enable: true,
            },
            {
                title: "Vendor Name",
                key: "vendorName",
                ordinal: 5,
                mandatory: false,
                enable: true,
            },
            // {
            //   title: "Vendor Id",
            //   key: "vendorId",
            //   ordinal: 6,
            //   mandatory: false,
            //   enable: true,
            // },
            {
                title: "Created At",
                key: "createdAt",
                ordinal: 8,
                mandatory: false,
                enable: true,
            },
            {
                title: "Created By",
                key: "createdByName",
                ordinal: 9,
                mandatory: false,
                enable: true,
            },
            {
                title: "Item Name",
                key: "itemName",
                ordinal: 10,
                mandatory: true,
                enable: true,
            },
            {
                title: "Item Code",
                key: "itemCode",
                ordinal: 11,
                mandatory: false,
                enable: true,
            },
            {
                title: "Category",
                key: "categoryName",
                ordinal: 12,
                mandatory: false,
                enable: true,
            },
            {
                title: "Sub Category",
                key: "subCategoryName",
                ordinal: 13,
                mandatory: false,
                enable: true,
            },
            // { title: "Package", key: "pkg", ordinal: 14, mandatory: false, enable: true,  },
            // { title: "Order Quantity", key: "orderQuantity", ordinal: 15, mandatory: false, enable: true,  },
            {
                title: "Quantity",
                key: "qty",
                ordinal: 16,
                mandatory: true,
                enable: true,
            },
            {
                title: "Package/UOM",
                key: "pkg",
                ordinal: 16,
                mandatory: true,
                enable: true,
            },
            {
                title: "Cost",
                key: "unitCost",
                ordinal: 17,
                mandatory: false,
                enable: true,
            },
            ...AMOUNT_DETAIL_COLUMNS,
        ],
        aggregateKey: null,
        options: {
            sortBy: "grnNumber",
            sortOrder: "desc",
        },
    },

    [REPORTS.VENDOR_WISE_GRN]: {
        id: REPORTS.VENDOR_WISE_GRN,
        name: "VENDOR-WISE GRN REPORT",
        headers: [
            {
                title: "Vendor Name",
                key: "vendorName",
                ordinal: 1,
                mandatory: true,
                enable: true,
            },
            {
                title: "Vendor Id",
                key: "vendorId",
                ordinal: 2,
                mandatory: false,
                enable: true,
            },
            ...AMOUNT_DETAIL_COLUMNS,
        ],
        aggregateKey: "vendorName",
        aggregateLabel: "vendorName",
        options: {
            sortBy: "totalAmount",
            sortOrder: "desc",
        },
    },

    [REPORTS.CATEGORY_WISE_GRN]: {
        id: REPORTS.CATEGORY_WISE_GRN,
        name: "CATEGORY-WISE GRN REPORT",
        headers: [
            {
                title: "Category",
                key: "categoryName",
                ordinal: 1,
                mandatory: true,
                enable: true,
            },
            // { title: "Sub Category", key: "subCategory", ordinal: 2, mandatory: true, enable: true,  },
            ...AMOUNT_DETAIL_COLUMNS,
        ],
        aggregateKey: "categoryId",
        aggregateLabel: "categoryName",
        options: {},
    },

    [REPORTS.ITEM_WISE_GRN]: {
        id: REPORTS.ITEM_WISE_GRN,
        name: "ITEM-WISE GRN REPORT",
        headers: [
            {
                title: "Item Name",
                key: "itemName",
                ordinal: 1,
                mandatory: true,
                enable: true,
            },
            {
                title: "Item Code",
                key: "itemCode",
                ordinal: 2,
                mandatory: false,
                enable: true,

            },
            {
                title: "HSN Code",
                key: "hsn",
                ordinal: 3,
                mandatory: false,
                enable: true,
            },
            {
                title: "Category",
                key: "categoryName",
                ordinal: 4,
                mandatory: false,
                enable: true,
            },
            {
                title: "Sub Category",
                key: "subCategoryName",
                ordinal: 5,
                mandatory: false,
                enable: true,
            },
            // { title: "UOM", key: "uom", ordinal: 5, mandatory: true, enable: true,  },
            {
                title: "Quantity",
                key: "qty",
                ordinal: 7,
                mandatory: true,
                enable: true,
            },
            {
                title: "Package/UOM",
                key: "pkg",
                ordinal: 8,
                mandatory: true,
                enable: true,
            },
            {
                title: "Unit Cost",
                key: "unitCost",
                ordinal: 9,
                mandatory: false,
                enable: true,
            },
            ...AMOUNT_DETAIL_COLUMNS,
        ],
        aggregateKey: "itemId",
        aggregateLabel: "itemName",
        options: {},
    },

    [REPORTS.LOCATION_WISE_GRN]: {
        id: REPORTS.LOCATION_WISE_GRN,
        name: "LOCATION-WISE GRN REPORT",
        headers: [
            {
                title: "Location Name",
                key: "locationName",
                ordinal: 1,
                mandatory: true,
                enable: true,
            },
            ...AMOUNT_DETAIL_COLUMNS,
        ],
        aggregateKey: "locationId",
        aggregateLabel: "locationName",
        options: {},
    },

    [REPORTS.DAILY_GRN]: {
        id: REPORTS.DAILY_GRN,
        name: "DAILY PURCHASE GRN REPORT",
        headers: [
            {
                title: "Date",
                key: "date",
                ordinal: 1,
                mandatory: true,
                enable: true,
            },
            ...AMOUNT_DETAIL_COLUMNS,
        ],
        aggregateKey: "date",
        aggregateLabel: "date",
        options: {},
    },

    [REPORTS.TRANSFER_LIST]: {
        id: REPORTS.TRANSFER_LIST,
        name: "TRANSFER REPORT",
        headers: [
            {
                title: "Transfer No",
                key: "transferNo",
                ordinal: 1,
                mandatory: true,
                enable: true,
            },
            {
                title: "Workarea From",
                key: "from",
                ordinal: 2,
                mandatory: true,
                enable: true,
            },
            {
                title: "Workarea To",
                key: "to",
                ordinal: 3,
                mandatory: false,
                enable: true,
            },
            {
                title: "Created At",
                key: "createdAt",
                ordinal: 4,
                mandatory: false,
                enable: true,
            },
            {
                title: "Created By",
                key: "createdBy",
                ordinal: 5,
                mandatory: false,
                enable: true,
            },
            {
                title: "Dispatch Status",
                key: "dispatchStatus",
                ordinal: 6,
                mandatory: false,
                enable: true,
            },
            {
                title: "Receive Status",
                key: "receiveStatus",
                ordinal: 7,
                mandatory: false,
                enable: true,
            },
        ],
        aggregateKey: null,
        options: {
            sortBy: "transferNumber",
            sortOrder: "desc",
        },
    },

    [REPORTS.DISPATCH_TRANSFER]: {
        id: REPORTS.DISPATCH_TRANSFER,
        name: "DISPATCH TRANSFER REPORT",
        headers: [
            {
                title: "Transfer No",
                key: "transferNo",
                ordinal: 1,
                mandatory: true,
                enable: true,
            },
            {
                title: "Dispatch No",
                key: "dispatchNo",
                ordinal: 2,
                mandatory: true,
                enable: true,
            },
            {
                title: "Dispatched At",
                key: "dispatchedAt",
                ordinal: 3,
                mandatory: false,
                enable: true,
            },
            {
                title: "Dispatched By",
                key: "dispatchedBy",
                ordinal: 4,
                mandatory: false,
                enable: true,
            },
            {
                title: "Receive Status",
                key: "status",
                ordinal: 5,
                mandatory: false,
                enable: true,
            },
        ],
        aggregateKey: null,
        options: {
            sortBy: "transferNumber",
            sortOrder: "desc",
        },
    },

    [REPORTS.DETAILED_TRANSFER]: {
        id: REPORTS.DETAILED_TRANSFER,
        name: "DETAILED TRANSFER REPORT",
        headers: [
            {
                title: "Transfer No",
                key: "transferNo",
                ordinal: 1,
                mandatory: true,
                enable: true,
            },
            {
                title: "From",
                key: "from",
                ordinal: 2,
                mandatory: true,
                enable: true,
            },
            {
                title: "To",
                key: "to",
                ordinal: 3,
                mandatory: false,
                enable: true,
            },
            {
                title: "Created At",
                key: "createdAt",
                ordinal: 4,
                mandatory: false,
                enable: true,
            },
            {
                title: "Created By",
                key: "createdBy",
                ordinal: 5,
                mandatory: false,
                enable: true,
            },
            {
                title: "Item Name",
                key: "itemName",
                ordinal: 6,
                mandatory: false,
                enable: true,
            },
            {
                title: "Item Code",
                key: "itemCode",
                ordinal: 7,
                mandatory: false,
                enable: true,
            },
            {
                title: "Package",
                key: "pkg",
                ordinal: 8,
                mandatory: false,
                enable: true,
            },
            {
                title: "Requested Quantity",
                key: "requestedQuantity",
                ordinal: 9,
                mandatory: false,
                enable: true,
            },
            {
                title: "Dispatched Quantity",
                key: "dispatchedQuantity",
                ordinal: 10,
                mandatory: false,
                enable: true,
            },
            {
                title: "Received Quantity",
                key: "receivedQuantity",
                ordinal: 11,
                mandatory: false,
                enable: true,
            },
        ],
        aggregateKey: null,
        options: {
            sortBy: "transferNumber",
            sortOrder: "desc",
        },
    },
});
