const _initAmt = () => {
  return {
    grossAmount: 0 /** unitPrice × quantity */,
    totalDiscount: 0 /** Sum of all discounts */,
    netAmount: 0 /** grossAmount - totalDiscount */,
    charges: [] /** Additional charges (packing, freight) */,
    totalChargeAmount: 0,
    taxes: [],
    totalTaxAmount: 0,
    totalAmount: 0,
    totalCess: 0, // @todo validate
    totalFocAmount: 0, // @todo validate
    taxRate: 0,
    otherTax: 0,
    roundoff: 0,
  };
};

const _calculate = (cart) => {
  // reset cart totals and taxes before recomputing
  cart.grossAmount = 0;
  cart.totalDiscount = 0;
  cart.netAmount = 0;
  cart.totalChargeAmount = 0;
  cart.totalTaxAmount = 0;
  cart.totalAmount = 0;
  cart.totalCess = 0;
  cart.totalFocAmount = 0;
  cart.taxes = []; // very important: start fresh

  cart.items?.forEach((item) => {
    // calculate item amount
    cart.grossAmount += item.grossAmount || 0;
    cart.totalDiscount += item.totalDiscount || 0;
    cart.netAmount += item.netAmount || 0;
    cart.totalChargeAmount += item.totalChargeAmount || 0;
    cart.totalTaxAmount += item.totalTaxAmount || 0;
    cart.totalCess += item.totalCess || 0;
    cart.totalFocAmount += item.totalFocAmount || 0;
    cart.totalAmount += item.totalAmount || 0;
    cart.taxes = _mergeTaxes(cart.taxes, item.taxes || []);
  });

  if (cart.charges.length) {
    cart.totalChargeAmount =
      cart.charges.reduce((acc, i) => acc + (Number(i.valueAmt) || 0), 0) || 0;
  }

  return {
    ...cart,
    totalAmount:
      cart.totalAmount - cart.totalFocAmount + cart.totalChargeAmount,
  };
};

const _mergeTaxes = (cartTaxes, itemTaxes) => {
  const result = [...cartTaxes.map((t) => ({ ...t }))];

  itemTaxes.forEach((itemTax) => {
    const existing = result.find((t) => t.id === itemTax.id);
    if (existing) {
      existing.value += itemTax.value;
    } else {
      result.push({ ...itemTax });
    }
  });

  return result;
};

export function NewCart(type = "po") {
  const cart = {
    type,
    location: null,
    items: [],
    deliveryDate: new Date(),
    vendor: null,
    remarks: null,
    ..._initAmt(),
  };
  return cart;
}

export function NewCartItem() {
  const item = {
    itemId: null,
    itemName: null,
    itemCode: null,
    categoryId: null,
    subcategoryId: null,
    hsnCode: null,
    vendor: null,
    pkg: null,
    purchaseUOM: null,
    remarks: null,
    quantity: 1,
    unitCost: 0,
    inStock: 0,
    ..._initAmt(),
  };
  return item;
}

export function _calculateItem(item, type) {
  const qty = type == "pr" ? item.quantity : item.receivedQty;

  item.grossAmount = Number(item.unitCost) * Number(qty);
  item.netAmount = Number(item.grossAmount) - Number(item.totalDiscount);
  item.totalChargeAmount = 0;

  // Calculate total tax rate (from selected taxes + other tax)
  const selectedTaxAmount = item.taxes.reduce(
    (acc, i) => acc + (Number(i.value) || 0),
    0
  );

  item.taxRate = Number(selectedTaxAmount) + Number(item.otherTax);

  // Apply tax on discounted grossAmount
  const totalTaxAmount =
    Number(item.netAmount * item.taxRate) / 100 + Number(item.totalCess);
  item.totalTaxAmount = totalTaxAmount >= 0 ? totalTaxAmount : 0;

  const totalAmount =
    Number(item.netAmount) +
    Number(item.totalTaxAmount) +
    Number(item.totalChargeAmount);
  item.totalAmount = totalAmount >= 0 ? totalAmount : 0;
  item.totalFocAmount = item.foc ? item.totalAmount : 0;

  return item;
}

export function AddItem(cart, item) {
  const computedItem = _calculateItem(item, cart.type);
  cart.items.unshift(computedItem);
  return _calculate(cart);
}

export function RemoveItem(cart, index) {
  cart.items.splice(index, 1);
  return _calculate(cart);
}

export function ModifyItem(cart, item, index) {
  const computedItem = _calculateItem(item, cart.type);
  cart.items[index] = computedItem;
  return _calculate(cart);
}

export function Calculate(cart) {
  return _calculate(cart);
}

export default {
  NewCart,
  NewCartItem,
  AddItem,
  RemoveItem,
  ModifyItem,
  Calculate,
};
