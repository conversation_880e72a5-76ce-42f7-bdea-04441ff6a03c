import { ref } from "vue";

export const tableHeaders = {
  vendors: ref([
    {
      title: "Name",
      key: "name",
      align: "start",
      sortable: true,
      mandatory: true,
      enable: true,
    },
    {
      title: "Vendor Id",
      key: "vendorId",
      align: "start",
      sortable: true,
      mandatory: false,
      enable: true,
    },
    {
      title: "Contact Name",
      key: "contactName",
      align: "start",
      sortable: true,
      mandatory: false,
      enable: true,
    },
    {
      title: "Contact No",
      key: "contactNo",
      align: "start",
      sortable: false,
      mandatory: false,
      enable: true,
    },
    {
      title: "Email",
      key: "contactEmailId",
      align: "start",
      sortable: false,
      mandatory: false,
      enable: true,
    },
    {
      title: "CIN",
      key: "cinNo",
      align: "start",
      sortable: false,
      class: "text-center",
      mandatory: false,
      enable: false,
    },
    {
      title: "GST",
      key: "gstNo",
      align: "start",
      sortable: false,
      mandatory: false,
      enable: false,
    },
    {
      title: "PAN",
      key: "panNo",
      align: "start",
      sortable: false,
      mandatory: false,
      enable: false,
    },
    {
      title: "TIN",
      key: "tinNo",
      align: "start",
      sortable: false,
      mandatory: false,
      enable: false,
    },
    {
      title: "Status",
      key: "activeStatus",
      align: "end",
      sortable: false,
      mandatory: true,
      enable: true,
    },
  ]),
  setVendors(newHeaders) {
    this.vendors.value = newHeaders;
  },
  categories: ref([
    {
      title: "Category Name",
      key: "name",
      align: "start",
      mandatory: true,
      enable: true,
    },
    {
      key: "data-table-expand", // optional, to keep it as short as possible
      align: "end",
      mandatory: true,
      enable: true,
    },
    {
      title: "Status",
      key: "activeStatus",
      align: "end",
      sortable: false,
      mandatory: true,
      enable: true,
    },
  ]),
  setCategories(newHeaders) {
    this.categories.value = newHeaders;
  },
  houseUnits: ref([
    {
      title: "Unit Name",
      key: "name",
      align: "left",
      sortable: true,
      mandatory: true,
      enable: true,
    },
    {
      title: "Unit Symbol",
      key: "symbol",
      align: "center",
      sortable: false,
      mandatory: true,
      enable: true,
    },
    {
      title: "Unit Type",
      key: "unitType",
      align: "center",
      sortable: false,
      mandatory: false,
      enable: true,
    },
    {
      title: "Conversions",
      key: "conversions",
      align: "center",
      sortable: false,
      mandatory: true,
      enable: true,
    },
    {
      title: "Status",
      key: "activeStatus",
      align: "end",
      sortable: false,
      mandatory: true,
      enable: true,
    },
  ]),
  setHouseUnits(newHeaders) {
    this.houseUnits.value = newHeaders;
  },
  inventoryItems: ref([
    {
      title: "Item Name",
      key: "itemName",
      align: "start",
      sortable: true,
      mandatory: true,
      enable: true,
    },
    {
      title: "Item Code",
      key: "itemCode",
      align: "center",
      sortable: false,
      mandatory: true,
      enable: true,
    },
    {
      title: "Item Type",
      key: "itemType",
      align: "center",
      sortable: false,
      mandatory: false,
      enable: true,
    },
    {
      title: "Category",
      key: "category",
      align: "center",
      sortable: false,
      mandatory: false,
      enable: true,
    },
    {
      title: "Sub Category",
      key: "subCategory",
      align: "center",
      sortable: false,
      mandatory: false,
      enable: true,
    },
    {
      title: "Vendors",
      key: "vendors",
      align: "center",
      sortable: false,
      mandatory: false,
      enable: false,
    },
    {
      title: "Purchase Unit",
      key: "purchaseUnit",
      align: "center",
      sortable: false,
      mandatory: false,
      enable: true,
    },
    {
      title: "Counting Unit",
      key: "countingUnit",
      align: "center",
      sortable: false,
      mandatory: false,
      enable: false,
    },
    {
      title: "Recipe Unit",
      key: "recipeUnit",
      align: "center",
      sortable: false,
      mandatory: false,
      enable: false,
    },
    {
      title: "Unit Cost",
      key: "unitCost",
      align: "center",
      sortable: false,
      mandatory: false,
      enable: false,
    },
    {
      title: "Par Level",
      key: "parLevel",
      align: "center",
      sortable: false,
      mandatory: false,
      enable: false,
    },
    {
      title: "Status",
      key: "activeStatus",
      align: "end",
      sortable: false,
      mandatory: true,
      enable: true,
    },
    // {
    //   title: "Actions",
    //   key: "actions",
    //   align: "end",
    //   sortable: false,
    //   mandatory: false,
    //   enable: true,
    // },
  ]),
  setInventoryItems(newHeaders) {
    this.inventoryItems.value = newHeaders;
  },
  recipes: ref([
    {
      title: "Name",
      key: "name",
      align: "start",
      sortable: true,
      mandatory: true,
      enable: true,
    },
    {
      title: "Recipe Code",
      key: "recipeCode",
      align: "center",
      sortable: false,
      mandatory: true,
      enable: true,
    },
    {
      title: "Type",
      key: "recipeType",
      align: "center",
      sortable: false,
      mandatory: false,
      enable: true,
    },
    {
      title: "Quantity",
      key: "quantity",
      align: "end",
      sortable: false,
      mandatory: false,
      enable: true,
    },
    {
      title: "Unit",
      key: "recipeUnit",
      align: "start",
      sortable: false,
      mandatory: false,
      enable: true,
    },
    {
      title: "Cost",
      key: "cost",
      align: "center",
      sortable: false,
      mandatory: false,
      enable: true,
    },
    {
      title: "Status",
      key: "activeStatus",
      align: "end",
      sortable: false,
      mandatory: true,
      enable: true,
    },
  ]),
  setRecipes(newHeaders) {
    this.recipes.value = newHeaders;
  },
  locations: ref([
    // {
    //   title: "",
    //   key: "actions",
    //   align: "center",
    //   sortable: true,
    //   mandatory: true,
    //   enable: true,
    // },
    {
      title: "Display Name",
      key: "name",
      align: "start",
      sortable: false,
      mandatory: true,
      enable: true,
    },
    {
      title: "Location Type",
      key: "locationType",
      align: "center",
      sortable: false,
      mandatory: true,
      enable: true,
    },
    {
      title: "Pos Id",
      key: "posId",
      align: "center",
      sortable: false,
      mandatory: false,
      enable: true,
    },
    {
      title: "PAN No.",
      key: "panNo",
      align: "center",
      sortable: false,
      mandatory: false,
      enable: true,
    },
    {
      title: "GST No.",
      key: "gstNo",
      align: "center",
      sortable: false,
      mandatory: false,
      enable: true,
    },
    {
      title: "Billing Address",
      key: "billTo",
      align: "center",
      sortable: false,
      mandatory: false,
      enable: true,
    },
    {
      title: "Shipping Address",
      key: "shipTo",
      align: "center",
      sortable: false,
      mandatory: false,
      enable: true,
    },
    {
      title: "Status",
      key: "activeStatus",
      align: "end",
      sortable: false,
      mandatory: true,
      enable: true,
    },
  ]),
  setLocations(newHeaders) {
    this.locations.value = newHeaders;
  },
  workArea: ref([
    {
      title: "Name",
      key: "name",
      align: "start",
      sortable: true,
      mandatory: true,
      enable: true,
    },
    {
      title: "Status",
      key: "activeStatus",
      align: "end",
      sortable: false,
      mandatory: true,
      enable: true,
    },
  ]),
  setWorkArea(newHeaders) {
    this.workArea.value = newHeaders;
  },
  tags: ref([
    {
      title: "Name",
      key: "name",
      align: "start",
      sortable: true,
      mandatory: true,
      enable: true,
    },
    {
      title: "Status",
      key: "activeStatus",
      align: "end",
      sortable: false,
      mandatory: true,
      enable: true,
    },
  ]),
  setTags(newHeaders) {
    this.tags.value = newHeaders;
  },
  taxes: ref([
    {
      title: "Name",
      key: "name",
      align: "start",
      sortable: true,
      mandatory: false,
      enable: true,
    },
    {
      title: "Value",
      key: "value",
      align: "center",
      sortable: true,
      mandatory: false,
      enable: true,
    },
    {
      key: "data-table-expand", // optional, to keep it as short as possible
      align: "end",
      mandatory: true,
      enable: true,
    },
    {
      title: "Status",
      key: "activeStatus",
      align: "end",
      sortable: false,
      mandatory: true,
      enable: true,
    },
  ]),
  setTaxes(newHeaders) {
    this.taxes.value = newHeaders;
  },
  users: ref([
    {
      title: "Email",
      key: "emailId",
      align: "start",
      sortable: true,
      mandatory: false,
      enable: true,
    },
    {
      title: "Name",
      key: "name",
      align: "center",
      mandatory: true,
      enable: true,
    },
    {
      title: "Role",
      key: "roleName",
      align: "center",
      sortable: false,
      mandatory: false,
      enable: true,
    },
    {
      title: "Verified",
      key: "verified",
      align: "center",
      mandatory: true,
      enable: true,
    },
    {
      title: "Status",
      key: "activeStatus",
      align: "end",
      sortable: false,
      mandatory: true,
      enable: true,
    },
  ]),
  setUsers(newHeaders) {
    this.users.value = newHeaders;
  },
  roles: ref([
    {
      title: "Name",
      key: "name",
      align: "start",
      sortable: true,
      mandatory: true,
      enable: true,
    },
    {
      title: "Privileges",
      key: "privileges",
      align: "center",
      sortable: false,
      mandatory: true,
      enable: true,
    },
    {
      title: "Status",
      key: "activeStatus",
      align: "end",
      sortable: false,
      mandatory: true,
      enable: true,
    },
  ]),
  setRoles(newHeaders) {
    this.roles.value = newHeaders;
  },

  closingListHeaders: ref([
    {
      title: "Closing No.",
      key: "closingNumber",
      align: "start",
      sortable: true,
      mandatory: true,
      enable: true,
    },
    {
      title: "Closing Date",
      key: "closingDate",
      align: "center",
      sortable: true,
      mandatory: true,
      enable: true,
    },
    {
      title: "Created Date",
      key: "createdDate",
      align: "center",
      sortable: false,
      mandatory: true,
      enable: true,
    },
    {
      title: "Created Time",
      key: "createdTime",
      align: "center",
      sortable: false,
      mandatory: false,
      enable: true,
    },
    {
      title: "Location",
      key: "locationName",
      align: "center",
      sortable: false,
      mandatory: false,
      enable: true,
    },
    {
      title: "WorkArea",
      key: "workAreaName",
      align: "end",
      sortable: false,
      mandatory: false,
      enable: true,
    },
  ]),
  setClosingListHeaders(newHeaders) {
    this.closingListHeaders.value = newHeaders;
  },
};

export const createClosingHeaders = [
  {
    title: "Item Name",
    key: "name",
    align: "start",
    sortable: true,
    mandatory: true,
    enable: true,
  },
  {
    title: "Item Code",
    key: "itemCode",
    align: "center",
    sortable: false,
    mandatory: true,
    enable: true,
  },
  {
    title: "Category",
    key: "category",
    align: "center",
    sortable: false,
    mandatory: false,
    enable: true,
  },
  {
    title: "Sub Category",
    key: "subCategory",
    align: "center",
    sortable: false,
    mandatory: false,
    enable: true,
  },
  {
    title: "Counting Unit",
    key: "countingUnit",
    align: "center",
    sortable: false,
    mandatory: false,
    enable: false,
  },
  {
    title: "Quantity",
    key: "quantity",
    align: "center",
    sortable: false,
    mandatory: false,
    enable: false,
  },
  {
    title: "Actions",
    key: "actions",
    align: "center",
    sortable: false,
    mandatory: false,
    enable: false,
  },
];

export const rolePrivilegeHeaders = [
  {
    title: "Label",
    key: "label",
    align: "start",
    sortable: true,
  },
  {
    title: "Description",
    key: "description",
    align: "start",
    sortable: false,
  },
  {
    title: "Access",
    key: "actions",
    align: "end",
    sortable: false,
  },
];

export const importExportLogHeaders = [
  {
    title: "Task Id",
    key: "id",
    align: "start",
    sortable: false,
  },
  {
    title: "Date & Time",
    key: "completedAt",
    align: "center",
    sortable: true,
  },
  {
    title: "Type",
    key: "type",
    align: "center",
    sortable: false,
  },
  {
    title: "File Name",
    key: "fileName",
    align: "center",
    sortable: true,
  },
  {
    title: "Uploaded By",
    key: "uploadedByName",
    align: "center",
    sortable: true,
  },
  {
    title: "Request Status",
    key: "requestStatus",
    align: "center",
    sortable: false,
  },
  {
    title: "Import Status",
    key: "importStatus",
    align: "center",
    sortable: false,
  },
  {
    title: "Errors",
    key: "logs",
    align: "center",
    sortable: false,
  },
];

export const conversionHeaders = [
  {
    title: "Quantity",
    key: "quantity",
    width: "40%",
    align: "left",
    type: "number",
    unit: true,
  },
  {
    title: "Unit Name",
    key: "name",
    width: "40%",
    align: "start",
    type: "text",
  },
  {
    title: "Actions",
    key: "actions",
    sortable: false,
    align: "center",
    width: "10%",
  },
];

export const packageHeaders = [
  {
    title: "#",
    key: "index",
    sortable: false,
    width: "5%",
    align: "center",
  },
  { title: "Name", key: "name", width: "40%", align: "start", sortable: false },
  {
    title: "Code",
    key: "packageCode",
    width: "20%",
    align: "center",
    sortable: false,
  },
  {
    title: "Quantity",
    key: "quantity",
    width: "15%",
    align: "center",
    sortable: false,
  },
  {
    title: "Unit Cost",
    key: "unitCost",
    width: "15%",
    align: "center",
    sortable: false,
  },
  {
    title: "Actions",
    key: "actions",
    sortable: false,
    align: "center",
    width: "10%",
  },
];

export const ingredientHeaders = [
  {
    title: "#",
    key: "index",
    width: "5%",
    align: "center",
    sortable: false,
  },
  {
    title: "Name",
    key: "name",
    width: "40%",
    align: "start",
    sortable: false,
  },
  {
    title: "Quantity",
    key: "quantity",
    width: "20%",
    align: "center",
    sortable: false,
  },
  {
    title: "Actions",
    key: "actions",
    sortable: false,
    align: "center",
    width: "10%",
  },
];

export const createTransferHeaders = [
  {
    title: "#",
    key: "index",
    align: "center",
    sortable: false,
  },
  {
    title: "Item Name",
    key: "itemName",
    align: "start",
    sortable: false,
  },
  {
    title: "InStock",
    key: "inStock",
    align: "center",
    sortable: false,
  },
  {
    title: "Quantity",
    key: "requestedQuantity",
    align: "center",
    sortable: false,
  },
  {
    title: "Cost",
    key: "unitCost",
    align: "center",
    sortable: false,
  },
  {
    title: "Total",
    key: "total",
    align: "end",
    sortable: false,
  },
  {
    title: "Actions",
    key: "actions",
    sortable: false,
    align: "center",
  },
];

export const recipeIngredientHeaders = [
  {
    title: "#",
    key: "index",
    sortable: false,
    width: "5%",
    align: "center",
  },
  {
    title: "Name",
    key: "name",
    width: "40%",
    align: "start",
    sortable: false,
  },
  {
    title: "Quantity",
    key: "quantity",
    width: "20%",
    align: "center",
    sortable: false,
  },
  {
    title: "Rate",
    key: "rate",
    width: "20%",
    align: "center",
    sortable: false,
  },
  {
    title: "Cost",
    key: "cost",
    width: "20%",
    align: "center",
    sortable: false,
  },
  {
    title: "Actions",
    key: "actions",
    sortable: false,
    align: "center",
    width: "10%",
  },
];

export const stockHeaders = [
  { title: "Item Name", key: "itemName", align: "start" },
  { title: "Item Code", key: "itemCode", align: "start" },
  { title: "Quantity", key: "qty", align: "end" },
  { title: "Unit", key: "uom", align: "start" },
  { title: "Location", key: "locationName", align: "start" },
  { title: "Work/Storage Area", key: "inventoryLocationName", align: "end" },
];

export const stockLogHeaders = [
  { title: "Location", key: "locationName", align: "start" },
  { title: "Work/Storage Area", key: "inventoryLocationName", align: "start" },
  { title: "Created At", key: "createdAt", align: "start", sortable: false },
  {
    title: "Item Name",
    key: "itemName",
    align: "start",
    sortable: false,
  },
  { title: "Type", key: "type", align: "start", sortable: false },
  { title: "Quantity", key: "qty", align: "end", sortable: false },
  {
    title: "Pkg",
    key: "uom",
    align: "start",
    sortable: false,
  },
  { title: "Summary", key: "remarks", align: "center", sortable: false },
];

export const prepareHeaders = [
  { title: "Item Name", key: "name", align: "start", sortable: false },
  { title: "Required", key: "required", align: "center", sortable: false },
  {
    title: "Available Quantity",
    key: "availableQuantity",
    align: "center",
    sortable: false,
  },
  { title: "Reason", key: "reason", align: "center", sortable: false },
];

export const purchaseRequestHeaders = [
  {
    title: "PR No",
    key: "prNumber",
    align: "start",
    sortable: true,
  },
  {
    title: "Vendor",
    key: "vendor",
    align: "start",
    sortable: false,
  },
  {
    title: "Created By",
    key: "requestedBy",
    align: "start",
    sortable: false,
  },
  {
    title: "Created At",
    key: "createdAt",
    align: "start",
    sortable: false,
  },
  {
    title: "Delivery Date",
    key: "deliveryDate",
    align: "start",
    sortable: false,
  },
  {
    title: "Total",
    key: "totalAmount",
    align: "end",
    sortable: false,
  },
  { title: "Status", key: "status", align: "end", sortable: false },
  { title: "", key: "action", align: "end", sortable: false, width: "5%" },
];

export const purchaseOrderHeaders = [
  {
    title: "PO No",
    key: "poNumber",
    align: "start",
    sortable: true,
  },
  {
    title: "Vendor",
    key: "vendor",
    align: "start",
    sortable: false,
  },
  {
    title: "Created By",
    key: "requestedBy",
    align: "start",
    sortable: false,
  },
  {
    title: "Created At",
    key: "createdAt",
    align: "start",
    sortable: false,
  },
  {
    title: "Total",
    key: "totalAmount",
    align: "end",
    sortable: false,
  },
  { title: "Status", key: "status", align: "end", sortable: false },
  { title: "", key: "action", align: "end", sortable: false, width: "5%" },
];

export const purchaseRequestItemHeaders = [
  {
    title: "#",
    key: "index",
    width: "5%",
    align: "center",
    sortable: false,
  },
  {
    title: "Item Name",
    key: "itemName",
    align: "start",
    sortable: false,
    minWidth: "200px",
  },
  {
    title: "Vendor",
    key: "vendor.name",
    align: "start",
    sortable: false,
  },
  {
    title: "Stock",
    key: "inStock",
    align: "start",
    sortable: false,
  },
  {
    title: "Quantity",
    key: "quantity",
    align: "center",
    width: "10%",
    sortable: false,
  },
  {
    title: "UOM",
    key: "purchaseUOM",
    align: "center",
    sortable: false,
  },
  {
    title: "Unit Cost",
    key: "unitCost",
    align: "center",
    width: "10%",
    sortable: false,
  },
  {
    title: "Discount",
    key: "totalDiscount",
    align: "center",
    width: "10%",
    sortable: false,
  },
  {
    title: "Cess",
    key: "totalCess",
    align: "center",
    width: "10%",
    sortable: false,
  },
  {
    title: "Tax Rate",
    key: "taxRate",
    align: "center",
    width: "10%",
    sortable: false,
    minWidth: "100px",
  },
  {
    title: "Total (excl.tax)",
    key: "netAmount",
    align: "end",
    width: "10%",
    sortable: false,
  },
  {
    title: "Tax Amt",
    key: "totalTaxAmount",
    align: "end",
    width: "10%",
    sortable: false,
  },
  {
    title: "Total (incl.tax)",
    key: "totalAmount",
    align: "end",
    width: "10%",
    sortable: false,
  },
  {
    key: "actions",
    align: "center",
    width: "5%",
    sortable: false,
  },
];

export const purchaseOrderItemHeaders = [
  {
    title: "#",
    key: "index",
    align: "center",
    sortable: false,
  },
  {
    title: "Item Name",
    key: "itemName",
    align: "start",
    sortable: false,
    minWidth: "200px",
  },
  {
    title: "Stock",
    key: "inStock",
    align: "start",
    sortable: false,
  },
  {
    title: "Order Qty",
    key: "orderedQty",
    align: "end",
    sortable: false,
  },
  {
    title: "Pending Qty",
    key: "pendingQty",
    align: "end",
    sortable: false,
  },
  {
    title: "Received Qty",
    key: "receivedQty",
    align: "end",
    sortable: false,
  },
  {
    title: "UOM",
    key: "purchaseUOM",
    align: "start",
    sortable: false,
  },
  {
    title: "Unit Cost",
    key: "unitCost",
    align: "end",
    sortable: false,
  },
  {
    title: "Discount",
    key: "totalDiscount",
    align: "end",
    sortable: false,
  },
  {
    title: "Cess",
    key: "totalCess",
    align: "center",
    sortable: false,
  },
  {
    title: "Tax Rate",
    key: "taxRate",
    align: "end",
    sortable: false,
    minWidth: "100px",
  },
  {
    title: "Total (excl.tax)",
    key: "netAmount",
    align: "end",
    sortable: false,
  },
  {
    title: "Tax Amt",
    key: "totalTaxAmount",
    align: "end",
    sortable: false,
  },
  {
    title: "Total (incl.tax)",
    key: "totalAmount",
    align: "end",
    sortable: false,
  },
];

export const grnListHeaders = [
  {
    title: "#",
    key: "index",
    width: "5%",
    align: "center",
    sortable: false,
  },
  {
    title: "Item Name",
    key: "itemName",
    align: "start",
    sortable: false,
    minWidth: "200px",
  },
  {
    title: "Order Qty",
    key: "orderedQty",
    align: "end",
    sortable: false,
  },
  {
    title: "Received Qty",
    key: "receivedQty",
    align: "end",
    sortable: false,
  },
  {
    title: "UOM",
    key: "uom",
    align: "start",
    sortable: false,
  },
  {
    title: "Unit Cost",
    key: "unitCost",
    align: "end",
    sortable: false,
  },
  {
    title: "Discount",
    key: "discount",
    align: "end",
    sortable: false,
  },
  {
    title: "Cess",
    key: "cess",
    align: "center",
    sortable: false,
  },
  {
    title: "Tax Rate",
    key: "taxRate",
    align: "end",
    sortable: false,
  },
  {
    title: "Total (excl.tax)",
    key: "totalExclusiveTax",
    align: "end",
    sortable: false,
  },
  {
    title: "Tax Amt",
    key: "taxAmount",
    align: "end",
    sortable: false,
  },
  {
    title: "Total (incl.tax)",
    key: "totalInclusiveTax",
    align: "end",
    sortable: false,
  },
];

export const menuItemHeaders = [
  { title: "Item Name", key: "itemName", align: "start", sortable: false },
  { title: "Item Code", key: "itemCode", align: "center", sortable: false },
  { title: "Status", key: "activeStatus", align: "center", sortable: false },
  {
    title: "Linking Status",
    key: "linkingStatus",
    align: "center",
    sortable: false,
  },
  { title: "Actions", key: "actions", align: "end", sortable: false },
];

export const menuItemIngredientHeaders = [
  { title: "Item Name", key: "itemName", align: "start", sortable: false },
  {
    title: "Item Type",
    key: "selectedItemType",
    align: "center",
    sortable: false,
  },
  { title: "Items", key: "selectedItemName", align: "center", sortable: false },
  {
    title: "Serving Quantity",
    key: "servingSizeName",
    align: "center",
    sortable: false,
  },
];

export const grnHeaders = [
  {
    title: "GRN No",
    key: "grnNumber",
    align: "start",
    sortable: true,
  },
  {
    title: "Invoice",
    key: "invoiceNumber",
    align: "start",
    sortable: false,
  },

  {
    title: "Vendor",
    key: "vendorName",
    align: "start",
    sortable: false,
  },
  {
    title: "Created By",
    key: "createdBy.name",
    align: "start",
    sortable: false,
  },
  {
    title: "Created At",
    key: "createdAt",
    align: "start",
    sortable: false,
  },
  {
    title: "Invoice Date",
    key: "invoiceDate",
    align: "start",
    sortable: false,
  },
  {
    title: "Total",
    key: "totalAmount",
    align: "end",
    sortable: false,
  },
  { title: "Status", key: "status", align: "end", sortable: false },
  { title: "", key: "action", align: "end", sortable: false, width: "5%" },
];

export const closedRequestItemHeaders = [
  {
    title: "Item Name",
    key: "itemName",
    width: "28%",
    align: "start",
    sortable: false,
  },

  {
    title: "Item Code",
    key: "itemCode",
    width: "12%",
    align: "center",
    sortable: false,
  },
  {
    title: "Vendor",
    key: "vendor",
    width: "20%",
    align: "center",
    sortable: false,
  },
  {
    title: "Quantity",
    key: "quantity",
    width: "10%",
    align: "end",
    sortable: false,
  },
  {
    title: "Unit Cost",
    key: "unitCost",
    width: "10%",
    align: "center",
    sortable: false,
  },
  {
    title: "Tax Rate",
    key: "taxRate",
    width: "10%",
    align: "center",
    sortable: false,
  },
  {
    title: "Total Price",
    key: "totalPrice",
    width: "10%",
    align: "center",
    sortable: false,
  },
];

export const closedOrderItemHeaders = [
  {
    title: "Item Name",
    key: "itemName",
    width: "28%",
    align: "start",
    sortable: false,
  },
  {
    title: "Item Code",
    key: "itemCode",
    width: "12%",
    align: "center",
    sortable: false,
  },
  {
    title: "Quantity",
    key: "quantity",
    width: "12%",
    align: "end",
    sortable: false,
  },

  {
    title: "Received Quantity",
    key: "receivedQuantity",
    width: "12%",
    align: "end",
    sortable: false,
  },
  {
    title: "Unit Cost",
    key: "unitCost",
    width: "12%",
    align: "center",
    sortable: false,
  },
  {
    title: "Tax Rate",
    key: "taxRate",
    width: "12%",
    align: "center",
    sortable: false,
  },
  {
    title: "Total Price",
    key: "totalPrice",
    width: "12%",
    align: "center",
    sortable: false,
  },
];

export const transferHeaders = ref([
  {
    title: "#",
    key: "transferNumber",
    align: "start",
    sortable: false,
    mandatory: false,
    enable: true,
  },
  {
    title: "Requester",
    key: "requester",
    align: "center",
    mandatory: true,
    enable: true,
  },
  {
    title: "Issuer",
    key: "issuer",
    align: "center",
    mandatory: true,
    enable: true,
  },
  {
    title: "Requested Date",
    key: "requestedDate",
    align: "center",
    sortable: false,
    mandatory: false,
    enable: true,
  },
  {
    title: "Requested Time",
    key: "requestedTime",
    align: "center",
    sortable: false,
    mandatory: false,
    enable: true,
  },
  {
    title: "Created By",
    key: "requestedBy",
    align: "center",
    sortable: false,
    mandatory: false,
    enable: true,
  },
  {
    title: "Dispatch History",
    key: "data-table-expand",
    align: "center",
    sortable: false,
    mandatory: false,
    enable: true,
  },
  {
    title: "Dispatch Status",
    key: "dispatchStatus",
    align: "center",
    sortable: false,
    mandatory: true,
    enable: true,
  },
  {
    title: "Receive Status",
    key: "receiveStatus",
    align: "center",
    sortable: false,
    mandatory: true,
    enable: true,
  },
  {
    title: "",
    key: "action",
    align: "end",
    sortable: false,
    mandatory: true,
    enable: true,
  },
]);

export const dispatchTransferHeaders = [
  {
    title: "Item Name",
    key: "itemName",
    align: "start",
    sortable: false,
    mandatory: false,
    enable: true,
  },
  {
    title: "Item Code",
    key: "itemCode",
    align: "center",
    sortable: false,
    mandatory: false,
    enable: true,
  },
  {
    title: "Package Name",
    key: "pkgName",
    align: "center",
    sortable: false,
    mandatory: false,
    default: true,
  },
  {
    title: "Available Qty",
    key: "availableQuantity",
    align: "center",
    mandatory: true,
    enable: true,
  },
  {
    title: "Requested Qty",
    key: "requestedQuantity",
    align: "center",
    mandatory: true,
    enable: true,
  },
  {
    title: "Pending Qty",
    key: "disPendingQuantity",
    align: "center",
    mandatory: true,
    enable: true,
  },
  {
    title: "Dispatch Qty",
    key: "dispatchedQuantity",
    align: "center",
    mandatory: true,
    enable: true,
  },
];

export const receiveTransferHeaders = [
  {
    title: "Item Name",
    key: "itemName",
    align: "start",
    sortable: false,
    mandatory: false,
    enable: true,
  },
  {
    title: "Item Code",
    key: "itemCode",
    align: "center",
    sortable: false,
    mandatory: false,
    enable: true,
  },
  {
    title: "Package Name",
    key: "pkgName",
    align: "center",
    sortable: false,
    mandatory: false,
    default: true,
  },
  {
    title: "Dispatched Qty",
    key: "dispatchedQuantity",
    align: "center",
    mandatory: true,
    enable: true,
  },
  {
    title: "Received Qty",
    key: "receivedQuantity",
    align: "center",
    mandatory: true,
    enable: true,
  },
  {
    title: "Shortage Qty",
    key: "shortageQuantity",
    align: "center",
    mandatory: true,
    enable: true,
  },
  {
    title: "Shortage Reason",
    key: "reason",
    align: "center",
    mandatory: true,
    enable: true,
  },
];

export const viewTransferHeaders = [
  {
    title: "Item Name",
    key: "itemName",
    align: "start",
    sortable: false,
    mandatory: false,
    enable: true,
  },
  {
    title: "Item Code",
    key: "itemCode",
    align: "center",
    sortable: false,
    mandatory: false,
    enable: true,
  },
  {
    title: "Package Name",
    key: "pkgName",
    align: "center",
    sortable: false,
    mandatory: false,
    default: true,
  },
  {
    title: "Requested Qty",
    key: "requestedQuantity",
    align: "center",
    mandatory: true,
    enable: true,
  },
  {
    title: "Dispatched Qty",
    key: "dispatchedQuantity",
    align: "center",
    mandatory: true,
    enable: true,
  },
  {
    title: "Received Qty",
    key: "receivedQuantity",
    align: "center",
    mandatory: true,
    enable: true,
  },
];

export const viewDispatchHeaders = [
  {
    title: "Item Name",
    key: "itemName",
    align: "start",
    sortable: false,
    mandatory: false,
    enable: true,
  },
  {
    title: "Item Code",
    key: "itemCode",
    align: "center",
    sortable: false,
    mandatory: false,
    enable: true,
  },
  {
    title: "Package Name",
    key: "pkgName",
    align: "center",
    sortable: false,
    mandatory: false,
    default: true,
  },
  {
    title: "Dispatched Qty",
    key: "dispatchedQuantity",
    align: "center",
    mandatory: true,
    enable: true,
  },
  {
    title: "Received Qty",
    key: "receivedQuantity",
    align: "center",
    mandatory: true,
    enable: true,
  },
  {
    title: "Shortage Qty",
    key: "shortageQuantity",
    align: "center",
    mandatory: true,
    enable: true,
  },
];

export const timelineHeaders = [
  {
    title: "Dispatch ID",
    key: "dispatchNo",
    align: "start",
    sortable: false,
  },
  {
    title: "Dispatched By",
    key: "dispatchedBy",
    align: "center",
    sortable: false,
  },
  {
    title: "Dispatched Date",
    key: "dispatchedDate",
    align: "center",
    sortable: false,
  },
  {
    title: "Dispatched Time",
    key: "dispatchedTime",
    align: "center",
    sortable: false,
  },
  {
    title: "Received Date",
    key: "receivedDate",
    align: "center",
    sortable: false,
  },
  {
    title: "Received Time",
    key: "receivedTime",
    align: "center",
    sortable: false,
  },
  {
    title: "Status",
    key: "status",
    align: "center",
    sortable: false,
  },
  {
    title: "",
    key: "actions",
    align: "end",
    sortable: false,
    width: "150px",
  },
];

export const contractHeaders = [
  {
    title: "#",
    key: "contractNumber",
    align: "start",
    sortable: true,
  },
  {
    title: "Contract Name",
    key: "name",
    align: "center",
    sortable: true,
  },
  {
    title: "Reference",
    key: "reference",
    align: "center",
    sortable: false,
  },
  {
    title: "Vendor",
    key: "vendor",
    align: "center",
    sortable: false,
  },
  {
    title: "Start Date",
    key: "startDate",
    align: "center",
    sortable: false,
  },

  {
    title: "Expiry Date",
    key: "endDate",
    align: "center",
    sortable: false,
  },
  {
    title: "Created By",
    key: "requestedBy",
    align: "center",
    sortable: false,
  },
  {
    title: "Created At",
    key: "createdAt",
    align: "center",
    sortable: false,
  },
  {
    title: "Status",
    key: "status",
    align: "center",
    sortable: false,
  },
  {
    title: "",
    key: "action",
    align: "end",
    sortable: false,
  },
];

export const contractItemHeaders = [
  {
    title: "#",
    key: "index",
    sortable: false,
    align: "center",
  },
  {
    title: "Item Name",
    key: "itemName",
    align: "start",
    sortable: false,
  },
  {
    title: "Contract Type",
    key: "contractType",
    align: "center",
    sortable: false,
  },
  {
    title: "Contract Price",
    key: "contractPrice",
    align: "center",
    sortable: false,
  },
  {
    title: "Actions",
    key: "actions",
    sortable: false,
    align: "center",
  },
];

export const contractViewHeaders = [
  {
    title: "#",
    key: "index",
    sortable: false,
    align: "center",
  },
  {
    title: "Item Name",
    key: "itemName",
    align: "start",
    sortable: true,
  },
  {
    title: "Contract Type",
    key: "contractType",
    align: "center",
    sortable: true,
  },
  {
    title: "Contract Price",
    key: "contractPrice",
    align: "center",
    sortable: false,
  },
];

export const adjustmentListHeaders = [
  {
    title: "Location",
    key: "location",
    align: "start",
    sortable: false,
    width: "10%",
  },
  {
    title: "Inventory Location",
    key: "inventoryLocation",
    align: "start",
    sortable: false,
    width: "10%",
  },
  {
    title: "Created By",
    key: "requestedBy",
    align: "start",
    sortable: false,
    width: "10%",
  },
  {
    title: "Created At",
    key: "createdAt",
    align: "start",
    sortable: false,
    width: "10%",
  },
];

export const adjustmentItemHeaders = [
  {
    title: "#",
    key: "index",
    sortable: false,
    width: "5%",
    align: "center",
  },
  {
    title: "Item Name",
    key: "name",
    width: "%",
    align: "start",
  },
  {
    title: "Type",
    key: "adjustType",
    align: "center",
    sortable: false,
    width: "10%",
  },
  {
    title: "Adjust Qty",
    key: "quantity",
    align: "center",
    sortable: false,
    width: "10%",
  },
  {
    title: "Remarks",
    key: "remarks",
    width: "25%",
    align: "center",
    sortable: false,
  },
  {
    title: "Actions",
    key: "actions",
    sortable: false,
    align: "center",
    width: "5%",
  },
];

export const spoilageListHeaders = [
  {
    title: "Location",
    key: "location",
    align: "start",
    sortable: false,
    width: "10%",
  },
  {
    title: "Inventory Location",
    key: "inventoryLocation",
    align: "start",
    sortable: false,
    width: "10%",
  },
  {
    title: "Created By",
    key: "requestedBy",
    align: "start",
    sortable: false,
    width: "10%",
  },
  {
    title: "Created At",
    key: "createdAt",
    align: "start",
    sortable: false,
    width: "10%",
  },
];

export const spoilageItemHeaders = [
  {
    title: "#",
    key: "index",
    sortable: false,
    width: "5%",
    align: "center",
  },
  {
    title: "Item Name",
    key: "name",
    width: "%",
    align: "start",
  },
  {
    title: "Spoilage Qty",
    key: "quantity",
    align: "center",
    sortable: false,
    width: "10%",
  },
  {
    title: "Reason",
    key: "reason",
    width: "25%",
    align: "center",
    sortable: false,
  },
  {
    title: "Actions",
    key: "actions",
    sortable: false,
    align: "center",
    width: "5%",
  },
];
