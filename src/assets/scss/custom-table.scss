.table-bordered {
  border-collapse: collapse;
  width: 100%;

  /* Header background */
  th {
    background-color: rgb(var(--v-theme-surface-light));
  }

  /* Header vertical borders */
  th:not(:last-child) {
    border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
  }

  /* Data cell vertical borders */
  td:not(:last-child) {
    border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
  }
}

// @todo: validate and remove below lines
.sticky-bottom-row {
  position: sticky;
  bottom: 0;
  z-index: 2;
  height: var(--v-table-header-height);
  background-color: rgb(var(--v-theme-surface-light));
  color: rgba(var(--v-theme-on-surface-light), var(--v-high-emphasis-opacity));
}

.sticky-bottom-row td {
  padding: 0 16px;
}